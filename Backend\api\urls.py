from django.urls import path, include
from rest_framework.routers import DefaultRout<PERSON>
from rest_framework_simplejwt.views import TokenRefreshView

from .views import (
    UserViewSet,
    CompanyViewSet,
    CurrencyViewSet,
    CustomerViewSet,
    CustomerBalanceViewSet,
    DaybookViewSet,
    JamaEntryViewSet,
    NaamEntryViewSet,
    TransactionViewSet,
    CustomTokenObtainPairView,
    login_view,
    logout_view,
    current_user_view,
    # Legacy ViewSets for frontend compatibility
    DaybookTransactionViewSet,
    JamaTransactionViewSet,
    NaamTransactionViewSet,
    CustomerCurrencyBalanceViewSet,
    CustomerCurrencyTransactionViewSet,
)

# Initialize DefaultRouter
router = DefaultRouter()

# Main ERD-based endpoints
router.register(r'users', UserViewSet, basename='user')
router.register(r'companies', CompanyViewSet)
router.register(r'currencies', CurrencyViewSet, basename='currency')
router.register(r'customers', CustomerViewSet, basename='customer')
router.register(r'customer-balances', CustomerBalanceViewSet)
router.register(r'daybooks', DaybookViewSet)
router.register(r'jama-entries', JamaEntryViewSet)
router.register(r'naam-entries', NaamEntryViewSet)
router.register(r'transactions', TransactionViewSet)

# Legacy endpoints for frontend compatibility
router.register(r'daybook-transactions', DaybookTransactionViewSet, basename='daybook-transaction')
router.register(r'jama-transactions', JamaTransactionViewSet, basename='jama-transaction')
router.register(r'naam-transactions', NaamTransactionViewSet, basename='naam-transaction')
router.register(r'customer-currency-balances', CustomerCurrencyBalanceViewSet, basename='customer-currency-balance')
router.register(r'customer-currency-transactions', CustomerCurrencyTransactionViewSet, basename='customer-currency-transaction')

# Authentication URLs
auth_urlpatterns = [
    # Traditional login/logout endpoints
    path('login/', login_view, name='login'),
    path('logout/', logout_view, name='logout'),
    path('user/', current_user_view, name='current_user'),

    # JWT token endpoints
    path('token/', CustomTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
]

urlpatterns = [
    # Include router-generated URLs
    path('', include(router.urls)),

    # Authentication endpoints
    path('auth/', include(auth_urlpatterns)),

    # Additional endpoints can be added here as needed
]