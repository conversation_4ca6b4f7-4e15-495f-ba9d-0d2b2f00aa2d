@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for Sarafi System */
@layer base {
  * {
    font-family: 'Noto Sans Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  }

  body {
    @apply bg-gray-50 text-gray-900;
    direction: rtl;
    text-align: right;
  }

  html {
    direction: rtl;
  }
}

@layer components {
  /* Pashto/Arabic text styling */
  .pashto-text {
    font-family: 'Noto Sans Arabic', Arial, sans-serif;
    direction: rtl;
    text-align: right;
    line-height: 1.6;
  }

  /* Custom button styles */
  .btn-primary {
    @apply bg-sarafi-600 hover:bg-sarafi-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  /* Card styles */
  .card {
    @apply bg-white rounded-lg shadow-md border border-gray-200;
  }

  /* Input styles */
  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-sarafi-500 focus:border-transparent;
  }
}
