import React, { useState } from "react";
import { X, User, Mail, Phone, Shield, Eye, EyeOff } from "lucide-react";

const AddUserModal = ({ isOpen, onClose, onUserAdded, roles = [] }) => {
  const [formData, setFormData] = useState({
    username: "",
    full_name: "",
    email: "",
    phone: "",
    password: "",
    confirmPassword: "",
    role: "cashier",
    status: "active",
    permissions: {
      dashboard: { read: true, write: false, delete: false },
      customers: { read: true, write: false, delete: false },
      transactions: { read: true, write: false, delete: false },
      reports: { read: true, write: false, delete: false },
      settings: { read: false, write: false, delete: false },
      admin: { read: false, write: false, delete: false },
    },
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [errors, setErrors] = useState({});

  const permissions = [
    { key: "dashboard", label: "کورپاڼه" },
    { key: "customers", label: "پیرودونکي" },
    { key: "transactions", label: "راکړې ورکړې" },
    { key: "reports", label: "راپورونه" },
    { key: "settings", label: "تنظیمات" },
    { key: "admin", label: "اډمین پینل" },
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const handlePermissionChange = (permissionKey, type, value) => {
    setFormData((prev) => ({
      ...prev,
      permissions: {
        ...prev.permissions,
        [permissionKey]: {
          ...prev.permissions[permissionKey],
          [type]: value,
        },
      },
    }));
  };

  const handleRoleChange = (role) => {
    const selectedRole = roles.find((r) => r.value === role);
    const defaultPermissions =
      selectedRole?.permissions || formData.permissions;

    setFormData((prev) => ({
      ...prev,
      role,
      permissions: defaultPermissions,
    }));
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.username.trim()) {
      newErrors.username = "د کاروونکي نوم اړین دی";
    }

    if (!formData.full_name.trim()) {
      newErrors.full_name = "بشپړ نوم اړین دی";
    }

    if (!formData.email.trim()) {
      newErrors.email = "بریښنالیک اړین دی";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "د بریښنالیک بڼه سمه نده";
    }

    if (!formData.phone.trim()) {
      newErrors.phone = "د تلیفون شمیره اړینه ده";
    }

    if (!formData.password.trim()) {
      newErrors.password = "پټنوم اړین دی";
    } else if (formData.password.length < 6) {
      newErrors.password = "پټنوم باید لږ تر لږه ۶ توري ولري";
    }

    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "پټنومونه سره سمون نلري";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (validateForm()) {
      const newUser = {
        ...formData,
        created_at: new Date().toISOString().split("T")[0],
        last_login: "هیڅکله نه",
      };
      delete newUser.confirmPassword; // Remove confirm password from user data
      onUserAdded(newUser);
    }
  };

  if (!isOpen) return null;

  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4'>
      <div className='bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto'>
        <div className='flex items-center justify-between p-6 border-b border-gray-200'>
          <h2 className='text-xl font-semibold text-gray-900 pashto-text'>
            نوی کاروونکی اضافه کړئ
          </h2>
          <button
            onClick={onClose}
            className='text-gray-400 hover:text-gray-600 transition-colors'
          >
            <X className='h-6 w-6' />
          </button>
        </div>

        <form onSubmit={handleSubmit} className='p-6 space-y-6'>
          {/* Basic Information */}
          <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
            <div>
              <label className='block text-sm font-medium text-gray-700 pashto-text mb-2'>
                د کاروونکي نوم *
              </label>
              <div className='relative'>
                <User className='absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400' />
                <input
                  type='text'
                  name='username'
                  value={formData.username}
                  onChange={handleInputChange}
                  className={`w-full pr-10 pl-3 py-2 border rounded-lg focus:ring-2 focus:ring-sarafi-500 focus:border-sarafi-500 ${
                    errors.username ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder='د کاروونکي نوم ولیکئ'
                  dir='rtl'
                />
              </div>
              {errors.username && (
                <p className='mt-1 text-sm text-red-600 pashto-text'>
                  {errors.username}
                </p>
              )}
            </div>

            <div>
              <label className='block text-sm font-medium text-gray-700 pashto-text mb-2'>
                بشپړ نوم *
              </label>
              <input
                type='text'
                name='full_name'
                value={formData.full_name}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-sarafi-500 focus:border-sarafi-500 ${
                  errors.full_name ? "border-red-500" : "border-gray-300"
                }`}
                placeholder='بشپړ نوم ولیکئ'
                dir='rtl'
              />
              {errors.full_name && (
                <p className='mt-1 text-sm text-red-600 pashto-text'>
                  {errors.full_name}
                </p>
              )}
            </div>

            <div>
              <label className='block text-sm font-medium text-gray-700 pashto-text mb-2'>
                بریښنالیک *
              </label>
              <div className='relative'>
                <Mail className='absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400' />
                <input
                  type='email'
                  name='email'
                  value={formData.email}
                  onChange={handleInputChange}
                  className={`w-full pr-10 pl-3 py-2 border rounded-lg focus:ring-2 focus:ring-sarafi-500 focus:border-sarafi-500 ${
                    errors.email ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder='<EMAIL>'
                />
              </div>
              {errors.email && (
                <p className='mt-1 text-sm text-red-600 pashto-text'>
                  {errors.email}
                </p>
              )}
            </div>

            <div>
              <label className='block text-sm font-medium text-gray-700 pashto-text mb-2'>
                د تلیفون شمیره *
              </label>
              <div className='relative'>
                <Phone className='absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400' />
                <input
                  type='tel'
                  name='phone'
                  value={formData.phone}
                  onChange={handleInputChange}
                  className={`w-full pr-10 pl-3 py-2 border rounded-lg focus:ring-2 focus:ring-sarafi-500 focus:border-sarafi-500 ${
                    errors.phone ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder='0799123456'
                  dir='rtl'
                />
              </div>
              {errors.phone && (
                <p className='mt-1 text-sm text-red-600 pashto-text'>
                  {errors.phone}
                </p>
              )}
            </div>
          </div>

          {/* Password Fields */}
          <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
            <div>
              <label className='block text-sm font-medium text-gray-700 pashto-text mb-2'>
                پټنوم *
              </label>
              <div className='relative'>
                <input
                  type={showPassword ? "text" : "password"}
                  name='password'
                  value={formData.password}
                  onChange={handleInputChange}
                  className={`w-full pr-3 pl-10 py-2 border rounded-lg focus:ring-2 focus:ring-sarafi-500 focus:border-sarafi-500 ${
                    errors.password ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder='پټنوم ولیکئ'
                />
                <button
                  type='button'
                  onClick={() => setShowPassword(!showPassword)}
                  className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600'
                >
                  {showPassword ? (
                    <EyeOff className='h-4 w-4' />
                  ) : (
                    <Eye className='h-4 w-4' />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className='mt-1 text-sm text-red-600 pashto-text'>
                  {errors.password}
                </p>
              )}
            </div>

            <div>
              <label className='block text-sm font-medium text-gray-700 pashto-text mb-2'>
                د پټنوم تصدیق *
              </label>
              <div className='relative'>
                <input
                  type={showConfirmPassword ? "text" : "password"}
                  name='confirmPassword'
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  className={`w-full pr-3 pl-10 py-2 border rounded-lg focus:ring-2 focus:ring-sarafi-500 focus:border-sarafi-500 ${
                    errors.confirmPassword
                      ? "border-red-500"
                      : "border-gray-300"
                  }`}
                  placeholder='پټنوم بیا ولیکئ'
                />
                <button
                  type='button'
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600'
                >
                  {showConfirmPassword ? (
                    <EyeOff className='h-4 w-4' />
                  ) : (
                    <Eye className='h-4 w-4' />
                  )}
                </button>
              </div>
              {errors.confirmPassword && (
                <p className='mt-1 text-sm text-red-600 pashto-text'>
                  {errors.confirmPassword}
                </p>
              )}
            </div>
          </div>

          {/* Role and Status */}
          <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
            <div>
              <label className='block text-sm font-medium text-gray-700 pashto-text mb-2'>
                رول *
              </label>
              <div className='relative'>
                <Shield className='absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400' />
                <select
                  name='role'
                  value={formData.role}
                  onChange={(e) => handleRoleChange(e.target.value)}
                  className='w-full pr-10 pl-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sarafi-500 focus:border-sarafi-500'
                >
                  {roles.map((role) => (
                    <option key={role.value} value={role.value}>
                      {role.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div>
              <label className='block text-sm font-medium text-gray-700 pashto-text mb-2'>
                حالت
              </label>
              <select
                name='status'
                value={formData.status}
                onChange={handleInputChange}
                className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sarafi-500 focus:border-sarafi-500'
              >
                <option value='active'>فعال</option>
                <option value='inactive'>غیر فعال</option>
                <option value='pending'>د تصدیق په انتظار</option>
              </select>
            </div>
          </div>

          {/* Permissions */}
          <div>
            <h3 className='text-lg font-medium text-gray-900 pashto-text mb-4'>
              اجازې
            </h3>
            <div className='bg-gray-50 rounded-lg p-4'>
              <div className='grid grid-cols-1 gap-4'>
                {permissions.map((permission) => (
                  <div
                    key={permission.key}
                    className='flex items-center justify-between py-2'
                  >
                    <span className='text-sm font-medium text-gray-700 pashto-text'>
                      {permission.label}
                    </span>
                    <div className='flex items-center space-x-4'>
                      <label className='flex items-center'>
                        <input
                          type='checkbox'
                          checked={
                            formData.permissions[permission.key]?.read || false
                          }
                          onChange={(e) =>
                            handlePermissionChange(
                              permission.key,
                              "read",
                              e.target.checked
                            )
                          }
                          className='h-4 w-4 text-sarafi-600 focus:ring-sarafi-500 border-gray-300 rounded'
                        />
                        <span className='mr-2 text-sm text-gray-600 pashto-text'>
                          لوستل
                        </span>
                      </label>
                      <label className='flex items-center'>
                        <input
                          type='checkbox'
                          checked={
                            formData.permissions[permission.key]?.write || false
                          }
                          onChange={(e) =>
                            handlePermissionChange(
                              permission.key,
                              "write",
                              e.target.checked
                            )
                          }
                          className='h-4 w-4 text-sarafi-600 focus:ring-sarafi-500 border-gray-300 rounded'
                        />
                        <span className='mr-2 text-sm text-gray-600 pashto-text'>
                          لیکل
                        </span>
                      </label>
                      <label className='flex items-center'>
                        <input
                          type='checkbox'
                          checked={
                            formData.permissions[permission.key]?.delete ||
                            false
                          }
                          onChange={(e) =>
                            handlePermissionChange(
                              permission.key,
                              "delete",
                              e.target.checked
                            )
                          }
                          className='h-4 w-4 text-sarafi-600 focus:ring-sarafi-500 border-gray-300 rounded'
                        />
                        <span className='mr-2 text-sm text-gray-600 pashto-text'>
                          ړنګول
                        </span>
                      </label>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className='flex items-center justify-end space-x-4 pt-6 border-t border-gray-200'>
            <button
              type='button'
              onClick={onClose}
              className='px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sarafi-500'
            >
              <span className='pashto-text'>لغوه کول</span>
            </button>
            <button
              type='submit'
              className='px-4 py-2 text-sm font-medium text-white bg-sarafi-600 border border-transparent rounded-lg hover:bg-sarafi-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sarafi-500'
            >
              <span className='pashto-text'>کاروونکی اضافه کړئ</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddUserModal;
