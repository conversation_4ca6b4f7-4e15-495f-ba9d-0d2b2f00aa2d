import React from 'react';

const Pagination = ({ page, pageSize, total, onPageChange, onPageSizeChange, pageSizes = [5,10,20,50] }) => {
  const totalPages = Math.max(1, Math.ceil(total / pageSize));
  const canPrev = page > 1;
  const canNext = page < totalPages;

  return (
    <div className="flex items-center justify-between p-4 bg-white rounded-lg shadow-md">
      <div className="flex items-center space-x-2">
        <button
          className={`px-3 py-1.5 text-sm rounded-md border ${canPrev ? 'text-gray-700 hover:bg-gray-50' : 'text-gray-300 cursor-not-allowed'}`}
          onClick={() => canPrev && onPageChange(page - 1)}
          disabled={!canPrev}
        >
          مخکنی
        </button>
        <div className="text-sm pashto-text">
          پاڼه {page} / {totalPages}
        </div>
        <button
          className={`px-3 py-1.5 text-sm rounded-md border ${canNext ? 'text-gray-700 hover:bg-gray-50' : 'text-gray-300 cursor-not-allowed'}`}
          onClick={() => canNext && onPageChange(page + 1)}
          disabled={!canNext}
        >
          بله
        </button>
      </div>
      <div className="flex items-center space-x-2">
        <span className="text-sm text-gray-600 pashto-text">اندازه:</span>
        <select
          className="border rounded-md px-2 py-1 text-sm"
          value={pageSize}
          onChange={(e) => onPageSizeChange(Number(e.target.value))}
        >
          {pageSizes.map((s) => (
            <option key={s} value={s}>{s}</option>
          ))}
        </select>
      </div>
    </div>
  );
};

export default Pagination;

