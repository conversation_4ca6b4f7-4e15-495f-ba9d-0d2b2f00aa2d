// API Test Utility
import { 
  authAPI, 
  customersAPI, 
  daybooksAPI, 
  customerCurrencyTransactionsAPI,
  customerCurrencyBalancesAPI 
} from '../services/api.js';

export const testAPIIntegration = async () => {
  console.log('🚀 Starting API Integration Test...');
  
  try {
    // Test 1: Authentication
    console.log('1. Testing Authentication...');
    const loginResult = await authAPI.login({
      username: 'admin',
      password: 'admin123'
    });
    console.log('✅ Login successful:', loginResult.user.full_name);

    // Test 2: Create Customer
    console.log('2. Testing Customer Creation...');
    const customerData = {
      full_name: 'احمد علي خان',
      mobile_number: '0799123456',
      address: 'کابل، افغانستان',
      loan_limit: 50000,
      status: 'active'
    };
    const newCustomer = await customersAPI.create(customerData);
    console.log('✅ Customer created:', newCustomer.customer_code, newCustomer.full_name);

    // Test 3: Create Daybook
    console.log('3. Testing Daybook Creation...');
    const daybookData = {
      page_number: 1,
      date: new Date().toISOString().split('T')[0],
      status: 'open',
      notes: 'د ازموینې ورځني کتاب'
    };
    const newDaybook = await daybooksAPI.create(daybookData);
    console.log('✅ Daybook created:', newDaybook.page_number, newDaybook.date);

    // Test 4: Add Currency to Customer
    console.log('4. Testing Currency Transaction...');
    const currencyTransaction = {
      customer: newCustomer.id,
      currency_type: 'NAM',
      transaction_type: 'add',
      amount: 10000,
      description: 'د ازموینې نام اضافه کول'
    };
    const transaction = await customerCurrencyTransactionsAPI.create(currencyTransaction);
    console.log('✅ Currency transaction created:', transaction.amount, transaction.currency_type);

    // Test 5: Check Customer Balance
    console.log('5. Testing Balance Retrieval...');
    const balances = await customerCurrencyBalancesAPI.getAll({ customer: newCustomer.id });
    console.log('✅ Customer balances:', balances);

    // Test 6: Transfer Currency
    console.log('6. Testing Currency Transfer...');
    // Create another customer first
    const customer2Data = {
      full_name: 'فاطمه احمد',
      mobile_number: '0700987654',
      address: 'هرات، افغانستان',
      loan_limit: 30000,
      status: 'active'
    };
    const customer2 = await customersAPI.create(customer2Data);
    
    const transferData = {
      from_customer: newCustomer.id,
      to_customer: customer2.id,
      currency_type: 'NAM',
      amount: 2000,
      description: 'د ازموینې انتقال'
    };
    await customerCurrencyTransactionsAPI.transfer(transferData);
    console.log('✅ Currency transfer completed');

    // Test 7: Get All Data
    console.log('7. Testing Data Retrieval...');
    const customers = await customersAPI.getAll();
    const daybooks = await daybooksAPI.getAll();
    console.log('✅ Data retrieved - Customers:', customers.length, 'Daybooks:', daybooks.length);

    console.log('🎉 All API tests passed successfully!');
    return {
      success: true,
      message: 'All API integrations are working correctly',
      testResults: {
        authentication: true,
        customerCreation: true,
        daybookCreation: true,
        currencyTransactions: true,
        balanceRetrieval: true,
        currencyTransfer: true,
        dataRetrieval: true
      }
    };

  } catch (error) {
    console.error('❌ API Test Failed:', error);
    return {
      success: false,
      message: 'API integration test failed',
      error: error.message
    };
  }
};

// Quick test function for development
export const quickAPITest = async () => {
  try {
    console.log('🔍 Quick API Test...');
    
    // Test basic endpoints
    const customers = await customersAPI.getAll();
    const daybooks = await daybooksAPI.getAll();
    const balances = await customerCurrencyBalancesAPI.getAll();
    
    console.log('✅ Quick test results:');
    console.log('- Customers:', customers.length || 0);
    console.log('- Daybooks:', daybooks.length || 0);
    console.log('- Currency Balances:', balances.length || 0);
    
    return true;
  } catch (error) {
    console.error('❌ Quick test failed:', error);
    return false;
  }
};

// Test individual API endpoints
export const testEndpoint = async (endpointName) => {
  const tests = {
    customers: () => customersAPI.getAll(),
    daybooks: () => daybooksAPI.getAll(),
    balances: () => customerCurrencyBalancesAPI.getAll(),
    transactions: () => customerCurrencyTransactionsAPI.getAll(),
  };

  if (!tests[endpointName]) {
    console.error('Unknown endpoint:', endpointName);
    return false;
  }

  try {
    const result = await tests[endpointName]();
    console.log(`✅ ${endpointName} endpoint working:`, result);
    return true;
  } catch (error) {
    console.error(`❌ ${endpointName} endpoint failed:`, error);
    return false;
  }
};
