import React, { useState, useEffect } from "react";
import {
  Plus,
  Minus,
  ArrowRightLeft,
  Eye,
  Search,
  Filter,
  Download,
  Wallet,
  TrendingUp,
  TrendingDown,
  Users,
  DollarSign,
} from "lucide-react";
import Layout from "./shared/Layout";
import Table from "./shared/Table";
import Modal from "./shared/Modal";
import FormInput from "./shared/FormInput";
import Button from "./shared/Button";
import { useToast } from "./shared/Toast.jsx";
import SearchBar from "./shared/SearchBar.jsx";
import Badge from "./shared/Badge.jsx";
import EmptyState from "./shared/EmptyState.jsx";
import LoadingSkeleton from "./shared/LoadingSkeleton.jsx";
import Breadcrumb from "./shared/Breadcrumb.jsx";
import {
  customersAPI,
  customerCurrencyBalancesAPI,
  customerCurrencyTransactionsAPI,
} from "../services/api.js";

const CustomerCurrencyManager = () => {
  const toast = useToast();
  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState([]);
  const [filteredCustomers, setFilteredCustomers] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showTransferModal, setShowTransferModal] = useState(false);
  const [showTransactionsModal, setShowTransactionsModal] = useState(false);
  const [transactions, setTransactions] = useState([]);

  // Form data for adding currency
  const [formData, setFormData] = useState({
    customer_id: "",
    currency_type: "NAM",
    transaction_type: "add",
    amount: "",
    description: "",
  });

  // Transfer form data
  const [transferData, setTransferData] = useState({
    from_customer: "",
    to_customer: "",
    currency_type: "NAM",
    amount: "",
    description: "",
  });

  // Sample data - replace with API calls
  const sampleCustomers = [
    {
      id: 1,
      customer_code: "C001",
      full_name: "احمد علي خان",
      mobile_number: "۰۷۹۹۱۲۳۴۵۶",
      nam_balance: 15000,
      jama_balance: 25000,
      total_balance: 40000,
      last_transaction: "۱۴۰۲/۰۹/۱۵",
      status: "فعال",
    },
    {
      id: 2,
      customer_code: "C002",
      full_name: "فاطمه احمد",
      mobile_number: "۰۷۰۰۹۸۷۶۵۴",
      nam_balance: 8000,
      jama_balance: 12000,
      total_balance: 20000,
      last_transaction: "۱۴۰۲/۰۹/۱۴",
      status: "فعال",
    },
    {
      id: 3,
      customer_code: "C003",
      full_name: "محمد حسن رحیمي",
      mobile_number: "۰۷۸۸۵۵۵۱۲۳",
      nam_balance: 22000,
      jama_balance: 18000,
      total_balance: 40000,
      last_transaction: "۱۴۰۲/۰۹/۱۳",
      status: "فعال",
    },
  ];

  const sampleTransactions = [
    {
      id: 1,
      customer_name: "احمد علي خان",
      currency_type: "NAM",
      transaction_type: "add",
      amount: 5000,
      balance_before: 10000,
      balance_after: 15000,
      description: "د نقدو اضافه کول",
      created_at: "۱۴۰۲/۰۹/۱۵ - ۱۰:۳۰",
      created_by: "محمد احمد",
    },
    {
      id: 2,
      customer_name: "فاطمه احمد",
      currency_type: "JAMA",
      transaction_type: "subtract",
      amount: 3000,
      balance_before: 15000,
      balance_after: 12000,
      description: "د پیسو ایستل",
      created_at: "۱۴۰۲/۰۹/۱۴ - ۱۴:۲۰",
      created_by: "علي رضا",
    },
  ];

  useEffect(() => {
    loadCustomers();
  }, []);

  useEffect(() => {
    filterCustomers();
  }, [customers, searchTerm]);

  const loadCustomers = async () => {
    setLoading(true);
    try {
      const response = await customersAPI.getAll();
      const customersData = response.results || response;

      // Load currency balances for each customer
      const customersWithBalances = await Promise.all(
        customersData.map(async (customer) => {
          try {
            const balances = await customerCurrencyBalancesAPI.getAll({
              customer: customer.id,
            });
            const namBalance =
              balances.find((b) => b.currency_type === "NAM")?.balance || 0;
            const jamaBalance =
              balances.find((b) => b.currency_type === "JAMA")?.balance || 0;

            return {
              ...customer,
              nam_balance: namBalance,
              jama_balance: jamaBalance,
              total_balance: namBalance + jamaBalance,
              last_transaction: new Date().toLocaleDateString("fa-AF"),
            };
          } catch (error) {
            return {
              ...customer,
              nam_balance: 0,
              jama_balance: 0,
              total_balance: 0,
              last_transaction: new Date().toLocaleDateString("fa-AF"),
            };
          }
        })
      );

      setCustomers(customersWithBalances);
    } catch (error) {
      console.error("Error loading customers:", error);
      toast.show({
        type: "error",
        message: "د پیرودونکو د لوډولو کې ستونزه",
      });
      // Fallback to sample data
      setCustomers(sampleCustomers);
    } finally {
      setLoading(false);
    }
  };

  const filterCustomers = () => {
    let filtered = customers;
    if (searchTerm) {
      filtered = customers.filter(
        (customer) =>
          customer.full_name.includes(searchTerm) ||
          customer.customer_code.includes(searchTerm) ||
          customer.mobile_number.includes(searchTerm)
      );
    }
    setFilteredCustomers(filtered);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleTransferInputChange = (e) => {
    const { name, value } = e.target;
    setTransferData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleAddCurrency = async () => {
    // Validate form
    if (!formData.customer_id || !formData.amount) {
      toast.show({ type: "error", message: "ټول اړین ساحې ډک کړئ" });
      return;
    }

    try {
      const transactionData = {
        customer: parseInt(formData.customer_id),
        currency_type: formData.currency_type,
        transaction_type: formData.transaction_type,
        amount: parseFloat(formData.amount),
        description:
          formData.description ||
          `${formData.transaction_type === "add" ? "اضافه کول" : "کمول"} د ${
            formData.currency_type
          }`,
      };

      await customerCurrencyTransactionsAPI.create(transactionData);

      toast.show({ type: "success", message: "اسعار بریالیتوب سره اضافه شول" });
      setShowAddModal(false);
      resetForm();
      loadCustomers(); // Reload to get updated balances
    } catch (error) {
      console.error("Error adding currency:", error);
      toast.show({
        type: "error",
        message: "د اسعارو د اضافه کولو کې ستونزه",
      });
    }
  };

  const handleTransfer = async () => {
    // Validate transfer form
    if (
      !transferData.from_customer ||
      !transferData.to_customer ||
      !transferData.amount
    ) {
      toast.show({ type: "error", message: "ټول اړین ساحې ډک کړئ" });
      return;
    }

    if (transferData.from_customer === transferData.to_customer) {
      toast.show({
        type: "error",
        message: "د ورکونکي او اخیستونکي پیرودونکي نشي کولی ورته وي",
      });
      return;
    }

    try {
      const transferRequestData = {
        from_customer: parseInt(transferData.from_customer),
        to_customer: parseInt(transferData.to_customer),
        currency_type: transferData.currency_type,
        amount: parseFloat(transferData.amount),
        description:
          transferData.description || `انتقال د ${transferData.currency_type}`,
      };

      await customerCurrencyTransactionsAPI.transfer(transferRequestData);

      toast.show({ type: "success", message: "انتقال بریالیتوب سره ترسره شو" });
      setShowTransferModal(false);
      resetTransferForm();
      loadCustomers(); // Reload to get updated balances
    } catch (error) {
      console.error("Error transferring currency:", error);
      toast.show({
        type: "error",
        message: "د انتقال کې ستونزه",
      });
    }
  };

  const resetForm = () => {
    setFormData({
      customer_id: "",
      currency_type: "NAM",
      transaction_type: "add",
      amount: "",
      description: "",
    });
  };

  const resetTransferForm = () => {
    setTransferData({
      from_customer: "",
      to_customer: "",
      currency_type: "NAM",
      amount: "",
      description: "",
    });
  };

  const viewTransactions = async (customer) => {
    setSelectedCustomer(customer);
    setShowTransactionsModal(true);

    try {
      const response = await customerCurrencyTransactionsAPI.getAll({
        customer: customer.id,
      });
      setTransactions(response.results || response);
    } catch (error) {
      console.error("Error loading transactions:", error);
      toast.show({
        type: "error",
        message: "د معاملاتو د لوډولو کې ستونزه",
      });
      // Fallback to sample data
      setTransactions(sampleTransactions);
    }
  };

  // Statistics
  const totalCustomers = customers.length;
  const totalNamBalance = customers.reduce((sum, c) => sum + c.nam_balance, 0);
  const totalJamaBalance = customers.reduce(
    (sum, c) => sum + c.jama_balance,
    0
  );
  const totalBalance = totalNamBalance + totalJamaBalance;

  const statsCards = [
    {
      title: "ټول پیرودونکي",
      value: totalCustomers,
      icon: Users,
      color: "bg-blue-500",
    },
    {
      title: "ټول نام",
      value: totalNamBalance.toLocaleString(),
      icon: TrendingUp,
      color: "bg-green-500",
    },
    {
      title: "ټول جمعه",
      value: totalJamaBalance.toLocaleString(),
      icon: TrendingDown,
      color: "bg-purple-500",
    },
    {
      title: "ټول میزان",
      value: totalBalance.toLocaleString(),
      icon: DollarSign,
      color: "bg-orange-500",
    },
  ];

  const columns = [
    {
      key: "customer_code",
      title: "کوډ",
      sortable: true,
      render: (value) => <span className='font-mono font-medium'>{value}</span>,
    },
    {
      key: "full_name",
      title: "بشپړ نوم",
      sortable: true,
      render: (value) => (
        <span className='pashto-text font-medium'>{value}</span>
      ),
    },
    {
      key: "nam_balance",
      title: "نام میزان",
      sortable: true,
      render: (value) => (
        <Badge variant='success'>{value.toLocaleString()} نام</Badge>
      ),
    },
    {
      key: "jama_balance",
      title: "جمعه میزان",
      sortable: true,
      render: (value) => (
        <Badge variant='info'>{value.toLocaleString()} جمعه</Badge>
      ),
    },
    {
      key: "total_balance",
      title: "ټول میزان",
      sortable: true,
      render: (value) => (
        <Badge variant='primary'>{value.toLocaleString()}</Badge>
      ),
    },
    {
      key: "last_transaction",
      title: "وروستۍ معامله",
      render: (value) => <span className='text-sm text-gray-600'>{value}</span>,
    },
    {
      key: "actions",
      title: "عملیات",
      render: (_, row) => (
        <div className='flex space-x-2'>
          <Button
            variant='ghost'
            size='sm'
            onClick={() => viewTransactions(row)}
            icon={Eye}
          >
            لیدل
          </Button>
        </div>
      ),
    },
  ];

  return (
    <Layout title='د پیرودونکو اسعار'>
      <div className='space-y-6'>
        <Breadcrumb items={[{ label: "د پیرودونکو اسعار" }]} />

        {/* Header */}
        <div className='flex items-center justify-between'>
          <div>
            <h1 className='text-2xl font-bold text-gray-900 pashto-text'>
              د پیرودونکو اسعار
            </h1>
            <p className='text-gray-600 pashto-text'>د نام او جمعه مدیریت</p>
          </div>
          <div className='flex space-x-3'>
            <Button
              onClick={() => setShowTransferModal(true)}
              variant='secondary'
              icon={ArrowRightLeft}
            >
              انتقال
            </Button>
            <Button
              onClick={() => setShowAddModal(true)}
              variant='primary'
              icon={Plus}
            >
              اسعار اضافه کړئ
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
          {statsCards.map((card, index) => (
            <div key={index} className='bg-white rounded-lg shadow-md p-6'>
              <div className='flex items-center'>
                <div
                  className={`w-12 h-12 ${card.color} rounded-lg flex items-center justify-center ml-4`}
                >
                  <card.icon className='h-6 w-6 text-white' />
                </div>
                <div>
                  <p className='text-sm font-medium text-gray-600 pashto-text'>
                    {card.title}
                  </p>
                  <p className='text-2xl font-bold text-gray-900'>
                    {card.value}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Search and Filters */}
        <div className='bg-white p-4 rounded-lg shadow-md'>
          <SearchBar
            value={searchTerm}
            onChange={setSearchTerm}
            placeholder='د پیرودونکي نوم، کوډ یا د موبایل شمیره لټون...'
            onClear={() => setSearchTerm("")}
          />
        </div>

        {/* Customers Table */}
        <div className='bg-white rounded-lg shadow-md'>
          {loading ? (
            <LoadingSkeleton type='table' rows={5} />
          ) : filteredCustomers.length === 0 ? (
            <EmptyState
              icon={Wallet}
              title='هیڅ پیرودونکی ونه موندل شو'
              description='د نویو پیرودونکو د اضافه کولو لپاره پورته تنه کلیک وکړئ'
            />
          ) : (
            <Table
              columns={columns}
              data={filteredCustomers}
              loading={loading}
            />
          )}
        </div>

        {/* Add Currency Modal */}
        <Modal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          title='اسعار اضافه کړئ'
          size='lg'
        >
          <div className='space-y-4'>
            <FormInput
              label='پیرودونکی'
              name='customer_id'
              type='select'
              value={formData.customer_id}
              onChange={handleInputChange}
              required
              options={[
                { value: "", label: "پیرودونکی غوره کړئ" },
                ...customers.map((c) => ({
                  value: c.id,
                  label: `${c.customer_code} - ${c.full_name}`,
                })),
              ]}
            />

            <FormInput
              label='د اسعارو ډول'
              name='currency_type'
              type='select'
              value={formData.currency_type}
              onChange={handleInputChange}
              required
              options={[
                { value: "NAM", label: "نام" },
                { value: "JAMA", label: "جمعه" },
              ]}
            />

            <FormInput
              label='د معاملې ډول'
              name='transaction_type'
              type='select'
              value={formData.transaction_type}
              onChange={handleInputChange}
              required
              options={[
                { value: "add", label: "اضافه کول" },
                { value: "subtract", label: "کمول" },
              ]}
            />

            <FormInput
              label='مقدار'
              name='amount'
              type='number'
              value={formData.amount}
              onChange={handleInputChange}
              placeholder='مقدار ولیکئ'
              required
            />

            <FormInput
              label='تفصیل'
              name='description'
              type='textarea'
              value={formData.description}
              onChange={handleInputChange}
              placeholder='د معاملې تفصیل ولیکئ'
              rows={3}
            />
          </div>

          <div className='flex justify-end space-x-3 mt-6'>
            <Button variant='outline' onClick={() => setShowAddModal(false)}>
              لغوه کول
            </Button>
            <Button variant='primary' onClick={handleAddCurrency}>
              ثبت کول
            </Button>
          </div>
        </Modal>

        {/* Transfer Modal */}
        <Modal
          isOpen={showTransferModal}
          onClose={() => setShowTransferModal(false)}
          title='د اسعارو انتقال'
          size='lg'
        >
          <div className='space-y-4'>
            <FormInput
              label='د ورکونکي پیرودونکی'
              name='from_customer'
              type='select'
              value={transferData.from_customer}
              onChange={handleTransferInputChange}
              required
              options={[
                { value: "", label: "ورکونکی پیرودونکی غوره کړئ" },
                ...customers.map((c) => ({
                  value: c.id,
                  label: `${c.customer_code} - ${c.full_name}`,
                })),
              ]}
            />

            <FormInput
              label='د اخیستونکي پیرودونکی'
              name='to_customer'
              type='select'
              value={transferData.to_customer}
              onChange={handleTransferInputChange}
              required
              options={[
                { value: "", label: "اخیستونکی پیرودونکی غوره کړئ" },
                ...customers.map((c) => ({
                  value: c.id,
                  label: `${c.customer_code} - ${c.full_name}`,
                })),
              ]}
            />

            <FormInput
              label='د اسعارو ډول'
              name='currency_type'
              type='select'
              value={transferData.currency_type}
              onChange={handleTransferInputChange}
              required
              options={[
                { value: "NAM", label: "نام" },
                { value: "JAMA", label: "جمعه" },
              ]}
            />

            <FormInput
              label='مقدار'
              name='amount'
              type='number'
              value={transferData.amount}
              onChange={handleTransferInputChange}
              placeholder='د انتقال مقدار ولیکئ'
              required
            />

            <FormInput
              label='تفصیل'
              name='description'
              type='textarea'
              value={transferData.description}
              onChange={handleTransferInputChange}
              placeholder='د انتقال تفصیل ولیکئ'
              rows={3}
            />
          </div>

          <div className='flex justify-end space-x-3 mt-6'>
            <Button
              variant='outline'
              onClick={() => setShowTransferModal(false)}
            >
              لغوه کول
            </Button>
            <Button variant='primary' onClick={handleTransfer}>
              انتقال ورکړئ
            </Button>
          </div>
        </Modal>

        {/* Transactions Modal */}
        <Modal
          isOpen={showTransactionsModal}
          onClose={() => setShowTransactionsModal(false)}
          title={`د ${selectedCustomer?.full_name} معاملات`}
          size='xl'
        >
          <div className='space-y-4'>
            {transactions.length === 0 ? (
              <EmptyState
                icon={Wallet}
                title='هیڅ معامله ونه موندل شوه'
                description='دا پیرودونکی تر اوسه هیڅ معامله نده کړې'
              />
            ) : (
              <div className='space-y-3'>
                {transactions.map((transaction) => (
                  <div
                    key={transaction.id}
                    className='border border-gray-200 rounded-lg p-4'
                  >
                    <div className='flex items-center justify-between'>
                      <div className='flex items-center space-x-3'>
                        <div
                          className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                            transaction.transaction_type === "add" ||
                            transaction.transaction_type === "transfer_in"
                              ? "bg-green-100"
                              : "bg-red-100"
                          }`}
                        >
                          {transaction.transaction_type === "add" ||
                          transaction.transaction_type === "transfer_in" ? (
                            <Plus className='h-5 w-5 text-green-600' />
                          ) : (
                            <Minus className='h-5 w-5 text-red-600' />
                          )}
                        </div>
                        <div>
                          <p className='font-medium text-gray-900 pashto-text'>
                            {transaction.currency_type} -{" "}
                            {transaction.amount.toLocaleString()}
                          </p>
                          <p className='text-sm text-gray-600 pashto-text'>
                            {transaction.description}
                          </p>
                        </div>
                      </div>
                      <div className='text-right'>
                        <p className='text-sm font-medium text-gray-900'>
                          میزان: {transaction.balance_after.toLocaleString()}
                        </p>
                        <p className='text-xs text-gray-500'>
                          {transaction.created_at}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </Modal>
      </div>
    </Layout>
  );
};

export default CustomerCurrencyManager;
