# Dependencies
Frontend/node_modules/
Backend/__pycache__/
Backend/*.pyc
Backend/*.pyo
Backend/*.pyd
Backend/.Python
Backend/env/
Backend/venv/
Backend/ENV/
Backend/env.bak/
Backend/venv.bak/

# Build outputs
Frontend/dist/
Frontend/build/
Backend/staticfiles/
# Backend/media/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Database
# Backend/db.sqlite3
# Backend/*.sqlite3

# Django
Backend/migrations/
Backend/*/migrations/
!Backend/*/migrations/__init__.py

# Cypress
Frontend/cypress/videos/
Frontend/cypress/screenshots/

# Temporary files
*.tmp
*.temp
bash.exe.stackdump
*.local
dist-ssr
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Archive files (compressed files)
*.zip
*.rar
*.7z
*.tar
*.tar.gz
*.tar.bz2
*.tar.xz
*.gz
*.bz2
*.xz
*.Z
*.lz
*.lzma
*.lzo
*.rz
*.sz
*.dz

# Python compiled files
*.pyc
*.pyo
*.pyd
__pycache__/
*.so
*.egg
*.egg-info/
dist/
build/
*.whl

# Backup files
*.bak
*.backup
*.old
*.orig
*.save
*~
*.swp
*.swo

# Package files
*.deb
*.rpm
*.msi
*.dmg
*.pkg
*.exe
*.app

# Media files (large files that shouldn't be in repo)
*.mp4
*.avi
*.mov
*.wmv
*.flv
*.webm
*.mkv
*.m4v
*.3gp
*.mp3
*.wav
*.flac
*.aac
*.ogg
*.wma

# Large image files
*.psd
*.ai
*.eps
*.tiff
*.tif
*.raw
*.cr2
*.nef
*.orf
*.sr2

# Documentation build files
docs/_build/
docs/build/
site/

# Cache directories
.cache/
.parcel-cache/
.next/
.nuxt/
.vuepress/dist/

# Test coverage
coverage/
.coverage
.pytest_cache/
htmlcov/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# Editor directories and files
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# Windows image file caches
Thumbs.db
ehthumbs.db

# Folder config file
Desktop.ini

# Recycle Bin used on file shares
$RECYCLE.BIN/

# Windows Installer files
*.cab
*.msi
*.msm
*.msp

# Windows shortcuts
*.lnk
