from rest_framework import permissions

class IsDayNotebookOwnerOrReadOnly(permissions.BasePermission):
    """
    Custom permission:
    - Safe methods (GET, HEAD, OPTIONS) allowed for all
    - Write methods require user to be the notebook creator
    """
    def has_object_permission(self, request, view, obj):
        if request.method in permissions.SAFE_METHODS:
            return True
        return obj.created_by == request.user
    

class IsDayNotebookOwnerForTransactions(permissions.BasePermission):
    """
    Allows access only if user owns the related daynotebook
    """
    def has_permission(self, request, view):
        # For list/create actions
        if request.method == 'GET':
            return True
            
        if request.method == 'POST':
            daynotebook_id = request.data.get('daynotebook')
            if not daynotebook_id:
                return False
            from core.models import DayNotebook
            return DayNotebook.objects.filter(
                id=daynotebook_id,
                created_by=request.user
            ).exists()
        
        return True

    def has_object_permission(self, request, view, obj):
        # For retrieve/update/delete actions
        if request.method == 'GET':
            return True
        return obj.daynotebook.created_by == request.user