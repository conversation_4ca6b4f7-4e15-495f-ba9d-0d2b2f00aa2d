from rest_framework import viewsets, permissions, status, mixins
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.authtoken.models import Token
from rest_framework_simplejwt.views import TokenObtainPairView
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from django.contrib.auth import authenticate
from django.db import IntegrityError
from rest_framework import serializers
from django.contrib.auth import get_user_model

from core.models import (
    User, Company, Currency, Customer, CustomerBalance, 
    Daybook, JamaEntry, NaamEntry, Transaction
)
from .serializers import (
    UserSerializer, CompanySerializer, CurrencySerializer, CustomerSerializer, 
    CustomerBalanceSerializer, DaybookSerializer, JamaEntrySerializer,
    NaamEntrySerializer, TransactionSerializer, CustomTokenObtainPairSerializer,
    # Legacy aliases for frontend compatibility
    DaybookTransactionSerializer, JamaTransactionSerializer,
    NaamTransactionSerializer, CustomerCurrencyBalanceSerializer, CustomerCurrencyTransactionSerializer
)

# ============================================================================
# AUTHENTICATION VIEWS
# ============================================================================

@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def login_view(request):
    """Login endpoint that returns DRF Token"""
    username = request.data.get('username')
    password = request.data.get('password')
    
    if not username or not password:
        return Response({'error': 'Username and password required'}, status=400)
    
    user = authenticate(username=username, password=password)
    if user:
        token, created = Token.objects.get_or_create(user=user)
        return Response({
            'token': token.key,
            'user': UserSerializer(user).data
        })
    else:
        return Response({'error': 'Invalid credentials'}, status=401)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def logout_view(request):
    """Logout endpoint that deletes the user's token"""
    try:
        request.user.auth_token.delete()
        return Response({'message': 'Successfully logged out'})
    except:
        return Response({'message': 'Logout successful'})

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def current_user_view(request):
    """Get current user info"""
    return Response(UserSerializer(request.user).data)

class CustomTokenObtainPairView(TokenObtainPairView):
    serializer_class = CustomTokenObtainPairSerializer

# ============================================================================
# MAIN VIEWSETS BASED ON ERD
# ============================================================================

class UserViewSet(viewsets.ModelViewSet):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAdminUser]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    search_fields = ['username', 'name', 'full_name', 'email']
    filterset_fields = ['role', 'status', 'is_active']
    ordering_fields = ['created_at', 'username']
    ordering = ['-created_at']

    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        user = self.get_object()
        user.is_active = True
        user.status = 'active'
        user.save()
        return Response({'message': 'User activated successfully'})

    @action(detail=True, methods=['post'])
    def deactivate(self, request, pk=None):
        user = self.get_object()
        user.is_active = False
        user.status = 'inactive'
        user.save()
        return Response({'message': 'User deactivated successfully'})

class CompanyViewSet(viewsets.ModelViewSet):
    queryset = Company.objects.all()
    serializer_class = CompanySerializer
    permission_classes = [permissions.IsAuthenticated]

class CurrencyViewSet(viewsets.ModelViewSet):
    queryset = Currency.objects.all()
    serializer_class = CurrencySerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['is_active', 'code']

class CustomerViewSet(viewsets.ModelViewSet):
    queryset = Customer.objects.all()
    serializer_class = CustomerSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter]
    search_fields = ['name', 'mobile_number']
    filterset_fields = ['is_active']

class CustomerBalanceViewSet(viewsets.ModelViewSet):
    queryset = CustomerBalance.objects.all()
    serializer_class = CustomerBalanceSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['customer', 'currency']

class DaybookViewSet(viewsets.ModelViewSet):
    queryset = Daybook.objects.all()
    serializer_class = DaybookSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['status', 'date', 'created_by']
    ordering_fields = ['date', 'page_number']
    ordering = ['-date', '-page_number']
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @action(detail=False, methods=['get'])
    def my_daybooks(self, request):
        """Get daybooks created by current user"""
        daybooks = self.queryset.filter(created_by=request.user)
        serializer = self.get_serializer(daybooks, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def next_page_number(self, request):
        """Get next page number for a given date"""
        date = request.query_params.get('date')
        if not date:
            return Response({'error': 'Date parameter required'}, status=400)
        
        last_daybook = Daybook.objects.filter(date=date).order_by('-page_number').first()
        next_page = (last_daybook.page_number + 1) if last_daybook else 1
        return Response({'next_page_number': next_page})

class JamaEntryViewSet(viewsets.ModelViewSet):
    queryset = JamaEntry.objects.all()
    serializer_class = JamaEntrySerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['daybook', 'customer', 'currency']

class NaamEntryViewSet(viewsets.ModelViewSet):
    queryset = NaamEntry.objects.all()
    serializer_class = NaamEntrySerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['daybook', 'customer', 'currency']

class TransactionViewSet(viewsets.ModelViewSet):
    queryset = Transaction.objects.all()
    serializer_class = TransactionSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['customer', 'currency', 'transaction_type', 'daybook']
    ordering_fields = ['created_at']
    ordering = ['-created_at']

# ============================================================================
# LEGACY VIEWSETS FOR FRONTEND COMPATIBILITY
# ============================================================================

# Alias ViewSets to match frontend expectations
DaybookTransactionViewSet = TransactionViewSet
JamaTransactionViewSet = JamaEntryViewSet
NaamTransactionViewSet = NaamEntryViewSet
CustomerCurrencyBalanceViewSet = CustomerBalanceViewSet
CustomerCurrencyTransactionViewSet = TransactionViewSet
