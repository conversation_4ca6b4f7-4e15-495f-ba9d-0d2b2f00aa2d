from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import (
    User, Company, Customer, CustomerBalance, Currency, 
    Daybook, JamaEntry, NaamEntry, Transaction
)

# ============================================================================
# ADMIN CONFIGURATION BASED ON ERD
# ============================================================================

class CustomUserAdmin(UserAdmin):
    """Custom User Admin based on ERD"""
    list_display = (
        'id', 'username', 'email', 'name', 'full_name', 'mobile_number',
        'role', 'status', 'is_active', 'is_superuser', 'created_at'
    )
    
    fieldsets = (
        (None, {'fields': ('username', 'password')}),
        ('Personal info', {'fields': ('name', 'full_name', 'email', 'mobile_number', 'phone')}),
        ('Role & Status', {'fields': ('role', 'status')}),
        ('Permissions', {
            'fields': (
                'is_active', 'is_staff', 'is_superuser',
                'groups', 'user_permissions'
            ),
        }),
        ('Important dates', {'fields': ('last_login', 'date_joined', 'created_at')}),
    )
    
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': (
                'username', 'name', 'full_name', 'mobile_number',
                'password1', 'password2', 'role', 'status',
                'is_staff', 'is_superuser'
            ),
        }),
    )
    
    list_filter = ('role', 'status', 'is_staff', 'is_superuser', 'is_active')
    search_fields = ('username', 'name', 'full_name', 'email', 'mobile_number')
    ordering = ('-created_at',)
    readonly_fields = ('created_at', 'updated_at')

admin.site.register(User, CustomUserAdmin)

@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    list_display = ('id', 'company_name', 'owner_name', 'mobile_number', 'created_at')
    search_fields = ('company_name', 'owner_name', 'mobile_number')
    readonly_fields = ('created_at', 'updated_at')
    list_filter = ('created_at',)

@admin.register(Currency)
class CurrencyAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'code', 'symbol', 'exchange_rate', 'is_active', 'created_at')
    search_fields = ('name', 'code')
    list_filter = ('is_active', 'created_at')
    readonly_fields = ('created_at', 'updated_at')

class CustomerBalanceInline(admin.TabularInline):
    model = CustomerBalance
    extra = 0
    readonly_fields = ['balance', 'last_transaction_date', 'created_at', 'updated_at']
    can_delete = False

@admin.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    inlines = [CustomerBalanceInline]
    list_display = ['id', 'name', 'mobile_number', 'is_active', 'created_at']
    search_fields = ['name', 'mobile_number']
    list_filter = ['is_active', 'created_at']
    readonly_fields = ('created_at', 'updated_at')

@admin.register(CustomerBalance)
class CustomerBalanceAdmin(admin.ModelAdmin):
    list_display = ['id', 'customer', 'currency', 'balance', 'last_transaction_date']
    list_filter = ['currency', 'last_transaction_date']
    search_fields = ['customer__name', 'currency__code']
    readonly_fields = ['balance', 'last_transaction_date', 'created_at', 'updated_at']

class JamaEntryInline(admin.TabularInline):
    model = JamaEntry
    extra = 0
    readonly_fields = ['customer_name', 'created_at', 'updated_at']

class NaamEntryInline(admin.TabularInline):
    model = NaamEntry
    extra = 0
    readonly_fields = ['customer_name', 'created_at', 'updated_at']

@admin.register(Daybook)
class DaybookAdmin(admin.ModelAdmin):
    inlines = [JamaEntryInline, NaamEntryInline]
    list_display = ('id', 'date', 'page_number', 'status', 'balance', 'created_by', 'created_at')
    list_filter = ('status', 'date', 'created_by')
    search_fields = ('created_by__username', 'created_by__name')
    readonly_fields = ('balance', 'created_at', 'updated_at')
    date_hierarchy = 'date'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('created_by')

@admin.register(JamaEntry)
class JamaEntryAdmin(admin.ModelAdmin):
    list_display = ('id', 'daybook', 'index', 'customer_name', 'amount', 'currency', 'created_at')
    list_filter = ('daybook', 'currency', 'created_at')
    search_fields = ('customer_name', 'customer__name', 'description')
    readonly_fields = ('customer_name', 'created_at', 'updated_at')
    raw_id_fields = ('daybook', 'customer', 'currency')
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('daybook', 'customer', 'currency')

@admin.register(NaamEntry)
class NaamEntryAdmin(admin.ModelAdmin):
    list_display = ('id', 'daybook', 'index', 'customer_name', 'amount', 'currency', 'created_at')
    list_filter = ('daybook', 'currency', 'created_at')
    search_fields = ('customer_name', 'customer__name', 'description')
    readonly_fields = ('customer_name', 'created_at', 'updated_at')
    raw_id_fields = ('daybook', 'customer', 'currency')
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('daybook', 'customer', 'currency')

@admin.register(Transaction)
class TransactionAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'customer', 'currency', 'transaction_type', 'amount', 
        'balance_before', 'balance_after', 'daybook', 'created_at'
    )
    list_filter = ('transaction_type', 'currency', 'daybook', 'created_at')
    search_fields = ('customer__name', 'description', 'created_by__username')
    readonly_fields = ('balance_before', 'balance_after', 'created_at')
    raw_id_fields = ('customer', 'currency', 'daybook', 'jama_entry', 'naam_entry', 'created_by')
    date_hierarchy = 'created_at'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'customer', 'currency', 'daybook', 'created_by'
        )
