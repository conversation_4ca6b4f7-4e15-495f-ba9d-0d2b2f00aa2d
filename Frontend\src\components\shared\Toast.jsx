import React, { createContext, useCallback, useContext, useMemo, useState } from 'react';
import { CheckCircle2, AlertTriangle, Info, XCircle, X } from 'lucide-react';

const ToastContext = createContext(null);

export const ToastProvider = ({ children }) => {
  const [toasts, setToasts] = useState([]);

  const remove = useCallback((id) => {
    setToasts((prev) => prev.filter((t) => t.id !== id));
  }, []);

  const show = useCallback((toast) => {
    const id = Date.now() + Math.random();
    const t = {
      id,
      type: toast.type || 'info',
      message: toast.message || '',
      duration: toast.duration ?? 3000,
    };
    setToasts((prev) => [...prev, t]);
    if (t.duration > 0) {
      setTimeout(() => remove(id), t.duration);
    }
    return id;
  }, [remove]);

  const value = useMemo(() => ({ show, remove }), [show, remove]);

  return (
    <ToastContext.Provider value={value}>
      {children}
      <ToastContainer toasts={toasts} onClose={remove} />
    </ToastContext.Provider>
  );
};

export const useToast = () => {
  const ctx = useContext(ToastContext);
  if (!ctx) throw new Error('useToast must be used within ToastProvider');
  return ctx;
};

const typeStyles = {
  success: 'bg-green-600 text-white',
  error: 'bg-red-600 text-white',
  warning: 'bg-yellow-600 text-white',
  info: 'bg-sarafi-600 text-white',
};

const TypeIcon = ({ type }) => {
  switch (type) {
    case 'success':
      return <CheckCircle2 className='h-5 w-5 ml-2' />;
    case 'error':
      return <XCircle className='h-5 w-5 ml-2' />;
    case 'warning':
      return <AlertTriangle className='h-5 w-5 ml-2' />;
    default:
      return <Info className='h-5 w-5 ml-2' />;
  }
};

const ToastContainer = ({ toasts, onClose }) => {
  return (
    <div className='fixed bottom-4 left-4 z-[1000] space-y-2'>
      {toasts.map((t) => (
        <div
          key={t.id}
          className={`shadow-lg rounded-lg px-4 py-3 flex items-center ${typeStyles[t.type]} max-w-xs`}
          role='status'
          aria-live='polite'
        >
          <TypeIcon type={t.type} />
          <div className='pashto-text text-sm'>{t.message}</div>
          <button
            className='ml-3 text-white/80 hover:text-white focus:outline-none'
            onClick={() => onClose(t.id)}
            aria-label='وتړئ'
          >
            <X className='h-4 w-4' />
          </button>
        </div>
      ))}
    </div>
  );
};

export default ToastProvider;

