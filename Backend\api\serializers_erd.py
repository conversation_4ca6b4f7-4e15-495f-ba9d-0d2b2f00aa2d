from rest_framework import serializers
from core.models import (
    User, Company, Currency, Customer, CustomerBalance, 
    Daybook, JamaEntry, NaamEntry, Transaction
)
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer

# ============================================================================
# SERIALIZERS BASED ON ERD SPECIFICATION
# ============================================================================

class UserSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'password', 'name', 'full_name', 'email',
            'mobile_number', 'phone', 'role', 'status', 'is_active',
            'is_staff', 'is_superuser', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
        extra_kwargs = {
            'password': {'write_only': True}
        }

    def create(self, validated_data):
        password = validated_data.pop('password')
        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()
        return user

    def update(self, instance, validated_data):
        password = validated_data.pop('password', None)
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        if password:
            instance.set_password(password)
        instance.save()
        return instance

class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    def validate(self, attrs):
        data = super().validate(attrs)
        user = self.user
        data.update({
            'id': user.id,
            'name': user.name,
            'username': user.username,
            'mobile_number': user.mobile_number,
            'is_active': user.is_active,
            'role': user.role
        })
        return data

class CompanySerializer(serializers.ModelSerializer):
    class Meta:
        model = Company
        fields = '__all__'
        read_only_fields = ['id', 'created_at', 'updated_at']

class CurrencySerializer(serializers.ModelSerializer):
    class Meta:
        model = Currency
        fields = '__all__'
        read_only_fields = ['id', 'created_at', 'updated_at']

class CustomerSerializer(serializers.ModelSerializer):
    class Meta:
        model = Customer
        fields = '__all__'
        read_only_fields = ['id', 'created_at', 'updated_at']

class CustomerBalanceSerializer(serializers.ModelSerializer):
    customer_name = serializers.CharField(source='customer.name', read_only=True)
    currency_name = serializers.CharField(source='currency.name', read_only=True)
    currency_code = serializers.CharField(source='currency.code', read_only=True)
    
    class Meta:
        model = CustomerBalance
        fields = '__all__'
        read_only_fields = ['id', 'last_transaction_date', 'created_at', 'updated_at']

class DaybookSerializer(serializers.ModelSerializer):
    created_by_name = serializers.CharField(source='created_by.full_name', read_only=True)
    jama_entries_count = serializers.SerializerMethodField()
    naam_entries_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Daybook
        fields = '__all__'
        read_only_fields = ['id', 'balance', 'created_at', 'updated_at']
    
    def get_jama_entries_count(self, obj):
        return obj.jama_entries.count()
    
    def get_naam_entries_count(self, obj):
        return obj.naam_entries.count()

class JamaEntrySerializer(serializers.ModelSerializer):
    customer_name = serializers.CharField(read_only=True)
    currency_code = serializers.CharField(source='currency.code', read_only=True)
    
    class Meta:
        model = JamaEntry
        fields = '__all__'
        read_only_fields = ['id', 'customer_name', 'created_at', 'updated_at']
    
    def create(self, validated_data):
        # Auto-populate customer_name from customer
        validated_data['customer_name'] = validated_data['customer'].name
        return super().create(validated_data)

class NaamEntrySerializer(serializers.ModelSerializer):
    customer_name = serializers.CharField(read_only=True)
    currency_code = serializers.CharField(source='currency.code', read_only=True)
    
    class Meta:
        model = NaamEntry
        fields = '__all__'
        read_only_fields = ['id', 'customer_name', 'created_at', 'updated_at']
    
    def create(self, validated_data):
        # Auto-populate customer_name from customer
        validated_data['customer_name'] = validated_data['customer'].name
        return super().create(validated_data)

class TransactionSerializer(serializers.ModelSerializer):
    customer_name = serializers.CharField(source='customer.name', read_only=True)
    currency_code = serializers.CharField(source='currency.code', read_only=True)
    created_by_name = serializers.CharField(source='created_by.full_name', read_only=True)
    
    class Meta:
        model = Transaction
        fields = '__all__'
        read_only_fields = [
            'id', 'balance_before', 'balance_after', 'created_at'
        ]

# ============================================================================
# LEGACY SERIALIZERS FOR FRONTEND COMPATIBILITY
# ============================================================================

# Alias serializers to match frontend expectations
DaybookTransactionSerializer = TransactionSerializer
JamaTransactionSerializer = JamaEntrySerializer
NaamTransactionSerializer = NaamEntrySerializer
CustomerCurrencyBalanceSerializer = CustomerBalanceSerializer
CustomerCurrencyTransactionSerializer = TransactionSerializer
