#!/usr/bin/env python3
"""
Script to populate the ERD-based database with sample data
"""
import os
import sys
import django
from decimal import Decimal
from datetime import date, datetime

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from core.models import (
    User, Company, Currency, Customer, CustomerBalance, 
    Daybook, JamaEntry, NaamEntry, Transaction
)

def populate_data():
    print("🚀 Populating ERD-based database with sample data...")
    
    # 1. Create Currencies
    print("💰 Creating currencies...")
    afn, _ = Currency.objects.get_or_create(
        code='AFN',
        defaults={
            'name': 'Afghan Afghani',
            'symbol': '؋',
            'color_hex': '#FF5733',
            'exchange_rate': Decimal('1.0000'),
            'is_active': True
        }
    )
    
    usd, _ = Currency.objects.get_or_create(
        code='USD',
        defaults={
            'name': 'US Dollar',
            'symbol': '$',
            'color_hex': '#28A745',
            'exchange_rate': Decimal('85.0000'),
            'is_active': True
        }
    )
    
    eur, _ = Currency.objects.get_or_create(
        code='EUR',
        defaults={
            'name': 'Euro',
            'symbol': '€',
            'color_hex': '#007BFF',
            'exchange_rate': Decimal('92.0000'),
            'is_active': True
        }
    )
    
    # 2. Create Customers
    print("👥 Creating customers...")
    customers_data = [
        {'name': 'احمد خان', 'mobile_number': '0701234567', 'address': 'کابل، افغانستان'},
        {'name': 'محمد علی', 'mobile_number': '0709876543', 'address': 'هرات، افغانستان'},
        {'name': 'فاطمه احمدی', 'mobile_number': '0705555555', 'address': 'مزار شریف، افغانستان'},
        {'name': 'علی رضا', 'mobile_number': '0703333333', 'address': 'جلال آباد، افغانستان'},
    ]
    
    customers = []
    for customer_data in customers_data:
        customer, created = Customer.objects.get_or_create(
            name=customer_data['name'],
            defaults=customer_data
        )
        customers.append(customer)
        
        # Create customer balances for each currency
        for currency in [afn, usd, eur]:
            CustomerBalance.objects.get_or_create(
                customer=customer,
                currency=currency,
                defaults={'balance': Decimal('0.00')}
            )
    
    # 3. Create a Company
    print("🏢 Creating company...")
    company, _ = Company.objects.get_or_create(
        company_name='صرافی سیستم',
        defaults={
            'owner_name': 'بسم الله وفادار',
            'mobile_number': '0701111111',
            'address': 'کابل، افغانستان'
        }
    )
    
    # 4. Get the existing user
    try:
        user = User.objects.get(username='khan')
        print(f"✅ Using existing user: {user.username}")
    except User.DoesNotExist:
        print("❌ User 'khan' not found. Please create a superuser first.")
        return
    
    # 5. Create Daybooks
    print("📖 Creating daybooks...")
    today = date.today()
    
    daybook1, _ = Daybook.objects.get_or_create(
        date=today,
        page_number=1,
        defaults={
            'status': 'open',
            'balance': Decimal('0.00'),
            'created_by': user
        }
    )
    
    daybook2, _ = Daybook.objects.get_or_create(
        date=today,
        page_number=2,
        defaults={
            'status': 'open',
            'balance': Decimal('0.00'),
            'created_by': user
        }
    )
    
    # 6. Create Jama Entries (Credits)
    print("💰 Creating Jama entries...")
    jama_entries = [
        {
            'daybook': daybook1,
            'index': 1,
            'customer': customers[0],
            'amount': Decimal('1000.00'),
            'currency': afn,
            'description': 'ابتدایی رصید'
        },
        {
            'daybook': daybook1,
            'index': 2,
            'customer': customers[1],
            'amount': Decimal('500.00'),
            'currency': usd,
            'description': 'ډالر واردات'
        },
    ]
    
    for entry_data in jama_entries:
        entry_data['customer_name'] = entry_data['customer'].name
        JamaEntry.objects.get_or_create(
            daybook=entry_data['daybook'],
            index=entry_data['index'],
            defaults=entry_data
        )
    
    # 7. Create Naam Entries (Debits)
    print("💸 Creating Naam entries...")
    naam_entries = [
        {
            'daybook': daybook1,
            'index': 1,
            'customer': customers[2],
            'amount': Decimal('750.00'),
            'currency': afn,
            'description': 'افغانۍ پیرودنه'
        },
        {
            'daybook': daybook2,
            'index': 1,
            'customer': customers[3],
            'amount': Decimal('200.00'),
            'currency': eur,
            'description': 'یورو پیرودنه'
        },
    ]
    
    for entry_data in naam_entries:
        entry_data['customer_name'] = entry_data['customer'].name
        NaamEntry.objects.get_or_create(
            daybook=entry_data['daybook'],
            index=entry_data['index'],
            defaults=entry_data
        )
    
    print("✅ Sample data populated successfully!")
    print(f"📊 Created:")
    print(f"   - {Currency.objects.count()} currencies")
    print(f"   - {Customer.objects.count()} customers")
    print(f"   - {CustomerBalance.objects.count()} customer balances")
    print(f"   - {Company.objects.count()} companies")
    print(f"   - {Daybook.objects.count()} daybooks")
    print(f"   - {JamaEntry.objects.count()} jama entries")
    print(f"   - {NaamEntry.objects.count()} naam entries")
    print(f"   - {Transaction.objects.count()} transactions")

if __name__ == '__main__':
    populate_data()
