import React, { useState, useEffect } from "react";
import {
  Plus,
  Edit,
  Trash2,
  Eye,
  Search,
  Filter,
  Download,
  FileText,
  CheckSquare,
  Square,
} from "lucide-react";
import Layout from "./shared/Layout";
import Table from "./shared/Table";
import Modal from "./shared/Modal";
import FormInput from "./shared/FormInput";
import Button from "./shared/Button";
import { useToast } from "./shared/Toast.jsx";
import Pagination from "./shared/Pagination.jsx";
import SearchBar from "./shared/SearchBar.jsx";
import Badge from "./shared/Badge.jsx";
import EmptyState from "./shared/EmptyState.jsx";
import LoadingSkeleton from "./shared/LoadingSkeleton.jsx";
import ConfirmDialog from "./shared/ConfirmDialog.jsx";
import Breadcrumb from "./shared/Breadcrumb.jsx";
import { customersAPI } from "../services/api.js";

const Customers = () => {
  const toast = useToast();
  const [customers, setCustomers] = useState([]);
  const [filteredCustomers, setFilteredCustomers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortField, setSortField] = useState("");
  const [sortDirection, setSortDirection] = useState("asc");
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [selectedCustomers, setSelectedCustomers] = useState([]);
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [showCustomerDetail, setShowCustomerDetail] = useState(false);
  const [detailCustomer, setDetailCustomer] = useState(null);
  const [showConfirmDelete, setShowConfirmDelete] = useState(false);

  // د مودال حالتونه
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState(null);

  // د فورم ډیټا
  const [formData, setFormData] = useState({
    customer_code: "",
    full_name: "",
    mobile_number: "",
    address: "",
    loan_limit: "500000",
  });
  const [formErrors, setFormErrors] = useState({});

  // د نمونه ډیټا (matches backend structure)
  const sampleCustomers = [
    {
      id: 1,
      customer_code: "C001",
      full_name: "احمد علي خان",
      mobile_number: "0799123456",
      address: "کابل، افغانستان",
      loan_limit: 500000,
      status: "active",
      created_at: "2024-01-15T10:30:00Z",
      updated_at: "2024-01-15T10:30:00Z",
    },
    {
      id: 2,
      customer_code: "C002",
      full_name: "فاطمه احمد",
      mobile_number: "0700987654",
      address: "هرات، افغانستان",
      loan_limit: 300000,
      status: "active",
      created_at: "2024-01-10T14:20:00Z",
      updated_at: "2024-01-10T14:20:00Z",
    },
    {
      id: 3,
      customer_code: "C003",
      full_name: "محمد حسن رحیمي",
      mobile_number: "0788555123",
      address: "مزار شریف، افغانستان",
      loan_limit: 750000,
      status: "active",
      created_at: "2024-01-05T09:15:00Z",
      updated_at: "2024-01-05T09:15:00Z",
    },
  ];

  useEffect(() => {
    loadCustomers();
  }, []);

  useEffect(() => {
    filterCustomers();
  }, [customers, searchTerm]);

  const loadCustomers = async () => {
    console.log("👥 Customers: Loading customers from backend...");
    setLoading(true);
    try {
      const response = await customersAPI.getAll();
      console.log("✅ Customers: Raw response:", response);

      const customersList = response.results || response;
      console.log(
        "✅ Customers: Processed customers list:",
        customersList?.length || 0
      );

      setCustomers(customersList);

      toast.show({
        type: "success",
        message: `${customersList?.length || 0} پیرودونکي لوډ شول`,
      });
    } catch (error) {
      console.error("❌ Customers: Error loading customers:", error);
      console.error("❌ Customers: Error details:", {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
      });

      toast.show({
        type: "error",
        message: "د پیرودونکو د لوډولو کې ستونزه - د نمونې ډیټا کارول کیږي",
      });

      // Fallback to sample data
      console.log("⚠️ Customers: Using fallback sample data");
      setCustomers(sampleCustomers);
    } finally {
      setLoading(false);
    }
  };

  const filterCustomers = () => {
    let filtered = customers;

    if (searchTerm) {
      filtered = filtered.filter(
        (customer) =>
          customer.full_name.includes(searchTerm) ||
          customer.customer_code.includes(searchTerm) ||
          customer.mobile_number.includes(searchTerm)
      );
    }

    setFilteredCustomers(filtered);
    setPage(1);
  };

  const handleSort = (field, direction) => {
    setSortField(field);
    setSortDirection(direction);

    const sorted = [...filteredCustomers].sort((a, b) => {
      if (direction === "asc") {
        return a[field] > b[field] ? 1 : -1;
      } else {
        return a[field] < b[field] ? 1 : -1;
      }
    });

    setFilteredCustomers(sorted);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // د غلطۍ پاکول
    if (formErrors[name]) {
      setFormErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.full_name.trim()) {
      errors.full_name = "د پیرودونکي نوم اړین دی";
    }

    if (!formData.mobile_number.trim()) {
      errors.mobile_number = "د موبایل شمیره اړینه ده";
    }

    if (!formData.loan_limit || formData.loan_limit <= 0) {
      errors.loan_limit = "د پور حد اړین دی";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleAddCustomer = async () => {
    console.log("👤 Customer: Starting customer creation...");

    if (!validateForm()) {
      console.log("❌ Customer: Form validation failed");
      return;
    }

    setLoading(true);
    try {
      const customerData = {
        full_name: formData.full_name,
        mobile_number: formData.mobile_number,
        address: formData.address,
        loan_limit: parseFloat(formData.loan_limit) || 0,
        status: "active",
      };

      console.log("👤 Customer: Sending customer data:", customerData);
      const response = await customersAPI.create(customerData);
      console.log("✅ Customer: Customer created successfully:", response);

      // Update the customers list with the new customer
      setCustomers((prev) => [...prev, response]);
      setShowAddModal(false);
      resetForm();

      toast.show({
        type: "success",
        message: `پیرودونکی ${response.full_name} په بریالیتوب سره اضافه شو - کوډ: ${response.customer_code}`,
      });

      console.log("✅ Customer: UI updated successfully");
    } catch (error) {
      console.error("❌ Customer: Error adding customer:", error);
      console.error("❌ Customer: Error details:", {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
      });

      let errorMessage = "د پیرودونکي د اضافه کولو کې ستونزه";

      if (error.response?.data) {
        if (typeof error.response.data === "string") {
          errorMessage = error.response.data;
        } else if (error.response.data.detail) {
          errorMessage = error.response.data.detail;
        } else if (error.response.data.error) {
          errorMessage = error.response.data.error;
        }
      }

      toast.show({
        type: "error",
        message: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEditCustomer = async () => {
    console.log("✏️ Customer: Starting customer edit...");

    if (!validateForm()) {
      console.log("❌ Customer: Edit form validation failed");
      return;
    }

    setLoading(true);
    try {
      const customerData = {
        full_name: formData.full_name,
        mobile_number: formData.mobile_number,
        address: formData.address,
        loan_limit: parseFloat(formData.loan_limit) || 0,
        status: formData.status || "active",
      };

      console.log(
        "✏️ Customer: Updating customer:",
        selectedCustomer.id,
        customerData
      );
      const response = await customersAPI.update(
        selectedCustomer.id,
        customerData
      );
      console.log("✅ Customer: Customer updated successfully:", response);

      // Update the customers list with the updated customer
      setCustomers((prev) =>
        prev.map((customer) =>
          customer.id === selectedCustomer.id ? response : customer
        )
      );

      setShowEditModal(false);
      resetForm();
      toast.show({
        type: "success",
        message: `د پیرودونکي ${response.full_name} معلومات بشپړ شول`,
      });

      console.log("✅ Customer: Edit UI updated successfully");
    } catch (error) {
      console.error("❌ Customer: Error updating customer:", error);
      console.error("❌ Customer: Update error details:", {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
      });

      let errorMessage = "د پیرودونکي د بشپړولو کې ستونزه";

      if (error.response?.data) {
        if (typeof error.response.data === "string") {
          errorMessage = error.response.data;
        } else if (error.response.data.detail) {
          errorMessage = error.response.data.detail;
        } else if (error.response.data.error) {
          errorMessage = error.response.data.error;
        }
      }

      toast.show({
        type: "error",
        message: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteCustomer = async () => {
    console.log("🗑️ Customer: Starting customer deletion...");

    setLoading(true);
    try {
      console.log("🗑️ Customer: Deleting customer:", selectedCustomer.id);
      await customersAPI.delete(selectedCustomer.id);
      console.log("✅ Customer: Customer deleted successfully");

      // Remove the customer from the list
      setCustomers((prev) =>
        prev.filter((customer) => customer.id !== selectedCustomer.id)
      );

      setShowDeleteModal(false);
      setSelectedCustomer(null);
      setShowConfirmDelete(false);

      toast.show({
        type: "success",
        message: `پیرودونکی ${selectedCustomer.full_name} ړنګ شو`,
      });

      console.log("✅ Customer: Delete UI updated successfully");
    } catch (error) {
      console.error("❌ Customer: Error deleting customer:", error);
      console.error("❌ Customer: Delete error details:", {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
      });

      let errorMessage = "د پیرودونکي د ړنګولو کې ستونزه";

      if (error.response?.data) {
        if (typeof error.response.data === "string") {
          errorMessage = error.response.data;
        } else if (error.response.data.detail) {
          errorMessage = error.response.data.detail;
        } else if (error.response.data.error) {
          errorMessage = error.response.data.error;
        }
      }

      toast.show({
        type: "error",
        message: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleBulkDelete = () => {
    setCustomers((prev) =>
      prev.filter((customer) => !selectedCustomers.includes(customer.id))
    );
    setSelectedCustomers([]);
    setShowBulkActions(false);
    toast.show({
      type: "success",
      message: `${selectedCustomers.length} پیرودونکي ړنګ شول`,
    });
  };

  const handleSelectCustomer = (customerId) => {
    setSelectedCustomers((prev) =>
      prev.includes(customerId)
        ? prev.filter((id) => id !== customerId)
        : [...prev, customerId]
    );
  };

  const handleSelectAll = () => {
    const currentPageCustomers = filteredCustomers.slice(
      (page - 1) * pageSize,
      page * pageSize
    );
    const allSelected = currentPageCustomers.every((customer) =>
      selectedCustomers.includes(customer.id)
    );

    if (allSelected) {
      setSelectedCustomers((prev) =>
        prev.filter(
          (id) => !currentPageCustomers.some((customer) => customer.id === id)
        )
      );
    } else {
      setSelectedCustomers((prev) => [
        ...prev,
        ...currentPageCustomers
          .map((customer) => customer.id)
          .filter((id) => !prev.includes(id)),
      ]);
    }
  };

  const openCustomerDetail = (customer) => {
    setDetailCustomer(customer);
    setShowCustomerDetail(true);
  };

  const handleExport = () => {
    toast.show({
      type: "info",
      message: "د پیرودونکو لیست د CSV په بڼه ډاونلوډ کیږي...",
    });
    // Mock export functionality
    setTimeout(() => {
      toast.show({ type: "success", message: "فایل بریالیتوب سره ډاونلوډ شو" });
    }, 2000);
  };

  const resetForm = () => {
    setFormData({
      customer_code: "",
      full_name: "",
      mobile_number: "",
      address: "",
      loan_limit: "500000",
    });
    setFormErrors({});
    setSelectedCustomer(null);
  };

  const openEditModal = (customer) => {
    setSelectedCustomer(customer);
    setFormData({
      customer_code: customer.customer_code,
      full_name: customer.full_name,
      mobile_number: customer.mobile_number,
      address: customer.address,
      loan_limit: customer.loan_limit.toString(),
    });
    setShowEditModal(true);
  };

  const openDeleteModal = (customer) => {
    setSelectedCustomer(customer);
    setShowDeleteModal(true);
  };

  const columns = [
    {
      key: "select",
      title: (
        <button onClick={handleSelectAll} className='flex items-center'>
          {filteredCustomers
            .slice((page - 1) * pageSize, page * pageSize)
            .every((customer) => selectedCustomers.includes(customer.id)) ? (
            <CheckSquare className='h-4 w-4' />
          ) : (
            <Square className='h-4 w-4' />
          )}
        </button>
      ),
      sortable: false,
      render: (_, customer) => (
        <button
          onClick={() => handleSelectCustomer(customer.id)}
          className='flex items-center'
        >
          {selectedCustomers.includes(customer.id) ? (
            <CheckSquare className='h-4 w-4 text-sarafi-600' />
          ) : (
            <Square className='h-4 w-4 text-gray-400' />
          )}
        </button>
      ),
    },
    {
      key: "customer_code",
      title: "د پیرودونکي کوډ",
      sortable: true,
      render: (value) => <span className='font-mono'>{value}</span>,
    },
    {
      key: "full_name",
      title: "بشپړ نوم",
      sortable: true,
      render: (value) => (
        <span className='pashto-text font-medium'>{value}</span>
      ),
    },
    {
      key: "mobile_number",
      title: "د موبایل شمیره",
      render: (value) => <span className='font-mono'>{value}</span>,
    },
    {
      key: "loan_limit",
      title: "د پور حد",
      sortable: true,
      render: (value) => {
        const limit = value || 0;
        return (
          <span className='font-mono'>{limit.toLocaleString()} افغانۍ</span>
        );
      },
    },
    {
      key: "status",
      title: "حالت",
      render: (value) => {
        const isActive = value === "active" || value === "فعال";
        const displayText = isActive ? "فعال" : "غیر فعال";
        return (
          <Badge variant={isActive ? "success" : "error"}>{displayText}</Badge>
        );
      },
    },
    {
      key: "actions",
      title: "کارونه",
      render: (_, customer) => (
        <div className='flex items-center space-x-2'>
          <Button
            variant='ghost'
            size='sm'
            onClick={() => openCustomerDetail(customer)}
          >
            <Eye className='h-4 w-4' />
          </Button>
          <button
            onClick={() => openEditModal(customer)}
            className='text-blue-600 hover:text-blue-800'
          >
            <Edit className='h-4 w-4' />
          </button>
          <button
            onClick={() => {
              setSelectedCustomer(customer);
              setShowConfirmDelete(true);
            }}
            className='text-red-600 hover:text-red-800'
          >
            <Trash2 className='h-4 w-4' />
          </button>
        </div>
      ),
    },
  ];

  return (
    <Layout title='د پیرودونکو مدیریت'>
      <div className='space-y-6'>
        <Breadcrumb items={[{ label: "پیرودونکي" }]} />

        {/* د پورته برخه */}
        <div className='flex items-center justify-between'>
          <div>
            <h1 className='text-2xl font-bold text-gray-900 pashto-text'>
              د پیرودونکو لیست
            </h1>
            <p className='text-gray-600 pashto-text'>
              ټول پیرودونکي: {filteredCustomers.length}
            </p>
          </div>
          <div className='flex space-x-3'>
            <Button onClick={handleExport} variant='secondary'>
              <Download className='h-4 w-4 ml-2' />
              صادرول
            </Button>
            <Button
              onClick={() => setShowAddModal(true)}
              icon={Plus}
              variant='primary'
            >
              نوی پیرودونکی اضافه کړئ
            </Button>
          </div>
        </div>

        {/* Bulk Actions */}
        {selectedCustomers.length > 0 && (
          <div className='bg-sarafi-50 border border-sarafi-200 rounded-lg p-4'>
            <div className='flex items-center justify-between'>
              <span className='text-sm text-sarafi-700 pashto-text'>
                {selectedCustomers.length} پیرودونکي غوره شوي
              </span>
              <div className='flex space-x-2'>
                <Button
                  variant='secondary'
                  size='sm'
                  onClick={() => setSelectedCustomers([])}
                >
                  لغوه کول
                </Button>
                <Button variant='danger' size='sm' onClick={handleBulkDelete}>
                  <Trash2 className='h-4 w-4 ml-1' />
                  ړنګول
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* د لټون او فلټر برخه */}
        <div className='bg-white p-4 rounded-lg shadow-md'>
          <div className='flex items-center space-x-4'>
            <div className='flex-1'>
              <SearchBar
                value={searchTerm}
                onChange={setSearchTerm}
                placeholder='د نوم، کوډ یا موبایل له مخې لټون...'
                onClear={() => setSearchTerm("")}
              />
            </div>
            <Button variant='outline' icon={Filter}>
              فلټر
            </Button>
          </div>
        </div>

        {/* د پیرودونکو جدول */}
        {loading ? (
          <LoadingSkeleton type='table' rows={pageSize} />
        ) : filteredCustomers.length === 0 ? (
          <div className='bg-white rounded-lg shadow-md'>
            <EmptyState
              title='هیڅ پیرودونکی نه دی موندل شوی'
              description='د نوي پیرودونکي اضافه کولو لپاره پورته تنۍ کلیک وکړئ'
              action={
                <Button onClick={() => setShowAddModal(true)} variant='primary'>
                  <Plus className='h-4 w-4 ml-2' />
                  نوی پیرودونکی
                </Button>
              }
            />
          </div>
        ) : (
          <>
            <Table
              columns={columns}
              data={filteredCustomers.slice(
                (page - 1) * pageSize,
                page * pageSize
              )}
              loading={loading}
              onSort={handleSort}
              sortField={sortField}
              sortDirection={sortDirection}
              emptyMessage='هیڅ پیرودونکی نه دی موندل شوی'
            />
            <div className='mt-4'>
              <Pagination
                page={page}
                pageSize={pageSize}
                total={filteredCustomers.length}
                onPageChange={setPage}
                onPageSizeChange={(s) => {
                  setPageSize(s);
                  setPage(1);
                }}
              />
            </div>
          </>
        )}
      </div>

      {/* د اضافه کولو مودال */}
      <Modal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        title='نوی پیرودونکی اضافه کړئ'
        size='lg'
      >
        <div className='space-y-4'>
          <FormInput
            label='بشپړ نوم'
            name='full_name'
            value={formData.full_name}
            onChange={handleInputChange}
            placeholder='د پیرودونکي بشپړ نوم ولیکئ'
            required
            error={formErrors.full_name}
          />

          <FormInput
            label='د موبایل شمیره'
            name='mobile_number'
            value={formData.mobile_number}
            onChange={handleInputChange}
            placeholder='۰۷۹۹۱۲۳۴۵۶'
            required
            error={formErrors.mobile_number}
          />

          <FormInput
            label='پته'
            name='address'
            type='textarea'
            value={formData.address}
            onChange={handleInputChange}
            placeholder='د پیرودونکي پته ولیکئ'
            rows={3}
          />

          <FormInput
            label='د پور حد (افغانۍ)'
            name='loan_limit'
            type='number'
            value={formData.loan_limit}
            onChange={handleInputChange}
            placeholder='500000'
            required
            error={formErrors.loan_limit}
          />

          <div className='flex justify-end space-x-3 pt-4'>
            <Button variant='secondary' onClick={() => setShowAddModal(false)}>
              لغوه کول
            </Button>
            <Button variant='primary' onClick={handleAddCustomer}>
              ثبت کول
            </Button>
          </div>
        </div>
      </Modal>

      {/* د تصحیح مودال */}
      <Modal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        title='د پیرودونکي معلومات تصحیح'
        size='lg'
      >
        <div className='space-y-4'>
          <FormInput
            label='بشپړ نوم'
            name='full_name'
            value={formData.full_name}
            onChange={handleInputChange}
            placeholder='د پیرودونکي بشپړ نوم ولیکئ'
            required
            error={formErrors.full_name}
          />

          <FormInput
            label='د موبایل شمیره'
            name='mobile_number'
            value={formData.mobile_number}
            onChange={handleInputChange}
            placeholder='۰۷۹۹۱۲۳۴۵۶'
            required
            error={formErrors.mobile_number}
          />

          <FormInput
            label='پته'
            name='address'
            type='textarea'
            value={formData.address}
            onChange={handleInputChange}
            placeholder='د پیرودونکي پته ولیکئ'
            rows={3}
          />

          <FormInput
            label='د پور حد (افغانۍ)'
            name='loan_limit'
            type='number'
            value={formData.loan_limit}
            onChange={handleInputChange}
            placeholder='500000'
            required
            error={formErrors.loan_limit}
          />

          <div className='flex justify-end space-x-3 pt-4'>
            <Button variant='secondary' onClick={() => setShowEditModal(false)}>
              لغوه کول
            </Button>
            <Button variant='primary' onClick={handleEditCustomer}>
              تصحیح کول
            </Button>
          </div>
        </div>
      </Modal>

      {/* د ړنګولو مودال */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title='د پیرودونکي ړنګول'
        size='md'
      >
        <div className='text-center'>
          <div className='mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4'>
            <Trash2 className='h-6 w-6 text-red-600' />
          </div>
          <h3 className='text-lg font-medium text-gray-900 mb-2 pashto-text'>
            ایا تاسو ډاډه یاست؟
          </h3>
          <p className='text-sm text-gray-500 mb-6 pashto-text'>
            دا عمل د "{selectedCustomer?.full_name}" پیرودونکی به د تل لپاره ړنګ
            کړي. دا عمل بیرته نشي کیدی.
          </p>
          <div className='flex justify-center space-x-3'>
            <Button
              variant='secondary'
              onClick={() => setShowDeleteModal(false)}
            >
              لغوه کول
            </Button>
            <Button variant='danger' onClick={handleDeleteCustomer}>
              ړنګول
            </Button>
          </div>
        </div>
      </Modal>

      {/* Customer Detail Modal */}
      <Modal
        isOpen={showCustomerDetail}
        onClose={() => setShowCustomerDetail(false)}
        title='د پیرودونکي تفصیلات'
        size='lg'
      >
        {detailCustomer && (
          <div className='space-y-6'>
            <div className='grid grid-cols-2 gap-4'>
              <div>
                <label className='block text-sm font-medium text-gray-700 pashto-text'>
                  کوډ
                </label>
                <p className='mt-1 text-sm text-gray-900 font-mono'>
                  {detailCustomer.customer_code}
                </p>
              </div>
              <div>
                <label className='block text-sm font-medium text-gray-700 pashto-text'>
                  نوم
                </label>
                <p className='mt-1 text-sm text-gray-900 pashto-text'>
                  {detailCustomer.full_name}
                </p>
              </div>
              <div>
                <label className='block text-sm font-medium text-gray-700 pashto-text'>
                  موبایل
                </label>
                <p className='mt-1 text-sm text-gray-900 font-mono'>
                  {detailCustomer.mobile_number}
                </p>
              </div>
              <div>
                <label className='block text-sm font-medium text-gray-700 pashto-text'>
                  حالت
                </label>
                <Badge
                  variant={
                    detailCustomer.status === "فعال" ? "success" : "error"
                  }
                >
                  {detailCustomer.status}
                </Badge>
              </div>
            </div>

            <div>
              <label className='block text-sm font-medium text-gray-700 pashto-text'>
                پته
              </label>
              <p className='mt-1 text-sm text-gray-900 pashto-text'>
                {detailCustomer.address}
              </p>
            </div>

            <div className='border-t pt-4'>
              <h4 className='text-lg font-medium text-gray-900 pashto-text mb-4'>
                وروستي معاملات
              </h4>
              <div className='space-y-2'>
                {[1, 2, 3].map((i) => (
                  <div
                    key={i}
                    className='flex justify-between items-center p-3 bg-gray-50 rounded'
                  >
                    <div>
                      <p className='text-sm font-medium pashto-text'>
                        معامله #{i}
                      </p>
                      <p className='text-xs text-gray-500'>۱۴۰۳/۰۲/۱۵</p>
                    </div>
                    <Badge variant='success'>+۵۰,۰۰۰ افغانۍ</Badge>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </Modal>

      {/* Confirm Delete Dialog */}
      <ConfirmDialog
        isOpen={showConfirmDelete}
        title='د پیرودونکي ړنګول'
        message='ایا تاسو ډاډه یاست چې غواړئ دا پیرودونکی ړنګ کړئ؟ دا عمل د بیرته راګرځولو وړ نه دی.'
        confirmText='هو، ړنګه یې'
        cancelText='نه، لغوه یې'
        onConfirm={handleDeleteCustomer}
        onCancel={() => setShowConfirmDelete(false)}
        variant='danger'
      />
    </Layout>
  );
};

export default Customers;
