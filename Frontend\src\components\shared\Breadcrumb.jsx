import React from 'react';
import { ChevronLeft, Home } from 'lucide-react';

const Breadcrumb = ({ items = [], className = '' }) => {
  return (
    <nav className={`flex ${className}`} aria-label="Breadcrumb">
      <ol className="inline-flex items-center space-x-1 md:space-x-3">
        <li className="inline-flex items-center">
          <a
            href="/dashboard"
            className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-sarafi-600"
          >
            <Home className="w-4 h-4 ml-2" />
            <span className="pashto-text">کورپاڼه</span>
          </a>
        </li>
        {items.map((item, index) => (
          <li key={index}>
            <div className="flex items-center">
              <ChevronLeft className="w-4 h-4 text-gray-400" />
              {item.href ? (
                <a
                  href={item.href}
                  className="mr-1 text-sm font-medium text-gray-700 hover:text-sarafi-600 md:mr-2 pashto-text"
                >
                  {item.label}
                </a>
              ) : (
                <span className="mr-1 text-sm font-medium text-gray-500 md:mr-2 pashto-text">
                  {item.label}
                </span>
              )}
            </div>
          </li>
        ))}
      </ol>
    </nav>
  );
};

export default Breadcrumb;
