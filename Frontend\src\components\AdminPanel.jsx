import React, { useState, useEffect } from "react";
import Layout from "./shared/Layout";
import {
  Users,
  Shield,
  Settings,
  Plus,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  Search,
  Filter,
  Download,
  Upload,
  UserCheck,
  UserX,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
} from "lucide-react";
import AddUserModal from "./admin/AddUserModal";
import EditUserModal from "./admin/EditUserModal";
import PermissionMatrix from "./admin/PermissionMatrix";
import RoleManagement from "./admin/RoleManagement";
import { useToast } from "./shared/Toast.jsx";

const AdminPanel = () => {
  const toast = useToast();
  const [activeTab, setActiveTab] = useState("users");
  const [users, setUsers] = useState([]);
  const [roles, setRoles] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterRole, setFilterRole] = useState("all");
  const [filterStatus, setFilterStatus] = useState("all");
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [showPermissionMatrix, setShowPermissionMatrix] = useState(false);

  // Mock data for users and roles
  useEffect(() => {
    // Initialize roles
    const initialRoles = [
      {
        value: "super_admin",
        label: "لوی مدیر",
        color: "bg-red-100 text-red-800",
        description: "د سیسټم بشپړ کنټرول او د ټولو برخو ته لاسرسی",
        permissions: {
          dashboard: { read: true, write: true, delete: true },
          customers: { read: true, write: true, delete: true },
          transactions: { read: true, write: true, delete: true },
          reports: { read: true, write: true, delete: true },
          settings: { read: true, write: true, delete: true },
          admin: { read: true, write: true, delete: true },
        },
      },
      {
        value: "manager",
        label: "مدیر",
        color: "bg-blue-100 text-blue-800",
        description: "د عمومي مدیریت دندې او د ډیرو برخو ته لاسرسی",
        permissions: {
          dashboard: { read: true, write: true, delete: false },
          customers: { read: true, write: true, delete: false },
          transactions: { read: true, write: true, delete: false },
          reports: { read: true, write: false, delete: false },
          settings: { read: true, write: false, delete: false },
          admin: { read: false, write: false, delete: false },
        },
      },
      {
        value: "cashier",
        label: "د پیسو ساتونکی",
        color: "bg-green-100 text-green-800",
        description: "د مالي راکړو ورکړو او پیرودونکو مدیریت",
        permissions: {
          dashboard: { read: true, write: false, delete: false },
          customers: { read: true, write: true, delete: false },
          transactions: { read: true, write: true, delete: false },
          reports: { read: true, write: false, delete: false },
          settings: { read: false, write: false, delete: false },
          admin: { read: false, write: false, delete: false },
        },
      },
      {
        value: "viewer",
        label: "لیدونکی",
        color: "bg-gray-100 text-gray-800",
        description: "یوازې د معلوماتو لیدل پرته د بدلولو څخه",
        permissions: {
          dashboard: { read: true, write: false, delete: false },
          customers: { read: true, write: false, delete: false },
          transactions: { read: true, write: false, delete: false },
          reports: { read: true, write: false, delete: false },
          settings: { read: false, write: false, delete: false },
          admin: { read: false, write: false, delete: false },
        },
      },
    ];
    setRoles(initialRoles);

    const mockUsers = [
      {
        id: 1,
        username: "admin",
        full_name: "احمد علي",
        email: "<EMAIL>",
        phone: "0799123456",
        role: "super_admin",
        status: "active",
        created_at: "2024-01-15",
        last_login: "2024-01-20 10:30",
        permissions: {
          dashboard: { read: true, write: true, delete: true },
          customers: { read: true, write: true, delete: true },
          transactions: { read: true, write: true, delete: true },
          reports: { read: true, write: true, delete: true },
          settings: { read: true, write: true, delete: true },
          admin: { read: true, write: true, delete: true },
        },
      },
      {
        id: 2,
        username: "manager",
        full_name: "فاطمه خان",
        email: "<EMAIL>",
        phone: "0700987654",
        role: "manager",
        status: "active",
        created_at: "2024-01-16",
        last_login: "2024-01-20 09:15",
        permissions: {
          dashboard: { read: true, write: true, delete: false },
          customers: { read: true, write: true, delete: false },
          transactions: { read: true, write: true, delete: false },
          reports: { read: true, write: false, delete: false },
          settings: { read: true, write: false, delete: false },
          admin: { read: false, write: false, delete: false },
        },
      },
      {
        id: 3,
        username: "cashier",
        full_name: "محمد حسن",
        email: "<EMAIL>",
        phone: "0788555123",
        role: "cashier",
        status: "active",
        created_at: "2024-01-17",
        last_login: "2024-01-20 08:45",
        permissions: {
          dashboard: { read: true, write: false, delete: false },
          customers: { read: true, write: true, delete: false },
          transactions: { read: true, write: true, delete: false },
          reports: { read: true, write: false, delete: false },
          settings: { read: false, write: false, delete: false },
          admin: { read: false, write: false, delete: false },
        },
      },
    ];
    setUsers(mockUsers);
  }, []);

  const tabs = [
    { id: "users", name: "د کاروونکو مدیریت", icon: Users },
    { id: "roles", name: "د رولونو مدیریت", icon: Shield },
    { id: "permissions", name: "د اجازو میټرکس", icon: Shield },
    { id: "settings", name: "د سیسټم تنظیمات", icon: Settings },
  ];

  const getRoleLabel = (role) => {
    const roleObj = roles.find((r) => r.value === role);
    return roleObj ? roleObj.label : role;
  };

  const getRoleColor = (role) => {
    const roleObj = roles.find((r) => r.value === role);
    return roleObj ? roleObj.color : "bg-gray-100 text-gray-800";
  };

  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesRole = filterRole === "all" || user.role === filterRole;
    const matchesStatus =
      filterStatus === "all" || user.status === filterStatus;

    return matchesSearch && matchesRole && matchesStatus;
  });

  const handleEditUser = (user) => {
    setSelectedUser(user);
    setShowEditModal(true);
  };

  const handleDeleteUser = (userId) => {
    if (window.confirm("ایا تاسو ډاډه یاست چې دا کاروونکی ړنګ کړئ؟")) {
      setUsers(users.filter((user) => user.id !== userId));
    }
  };

  const handleToggleStatus = (userId) => {
    setUsers(
      users.map((user) =>
        user.id === userId
          ? {
              ...user,
              status: user.status === "active" ? "inactive" : "active",
            }
          : user
      )
    );
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case "active":
        return <CheckCircle className='h-4 w-4 text-green-500' />;
      case "inactive":
        return <XCircle className='h-4 w-4 text-red-500' />;
      case "pending":
        return <Clock className='h-4 w-4 text-yellow-500' />;
      default:
        return <AlertTriangle className='h-4 w-4 text-gray-500' />;
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case "active":
        return "فعال";
      case "inactive":
        return "غیر فعال";
      case "pending":
        return "د تصدیق په انتظار";
      default:
        return "نامعلوم";
    }
  };

  return (
    <Layout title='د اډمین پینل'>
      <div className='space-y-6'>
        {/* Header */}
        <div className='bg-white rounded-lg shadow-sm p-6'>
          <div className='flex items-center justify-between'>
            <div>
              <h1 className='text-2xl font-bold text-gray-900 pashto-text'>
                د اډمین پینل
              </h1>
              <p className='text-gray-600 pashto-text mt-1'>
                د کاروونکو او اجازو مدیریت
              </p>
            </div>
            <div className='flex items-center space-x-3'>
              <button className='flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors'>
                <Download className='h-4 w-4 ml-2' />
                <span className='pashto-text'>د ډیټا ایکسپورټ</span>
              </button>
              <button className='flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors'>
                <Upload className='h-4 w-4 ml-2' />
                <span className='pashto-text'>د ډیټا امپورټ</span>
              </button>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className='bg-white rounded-lg shadow-sm'>
          <div className='border-b border-gray-200'>
            <nav className='flex space-x-8 px-6'>
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? "border-sarafi-500 text-sarafi-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
                >
                  <tab.icon className='h-5 w-5 ml-2' />
                  <span className='pashto-text'>{tab.name}</span>
                </button>
              ))}
            </nav>
          </div>

          <div className='p-6'>
            {activeTab === "users" && (
              <div className='space-y-6'>
                {/* Filters and Search */}
                <div className='flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0'>
                  <div className='flex items-center space-x-4'>
                    <div className='relative'>
                      <Search className='absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400' />
                      <input
                        type='text'
                        placeholder='د کاروونکي لټون...'
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className='pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sarafi-500 focus:border-sarafi-500'
                        dir='rtl'
                      />
                    </div>
                    <select
                      value={filterRole}
                      onChange={(e) => setFilterRole(e.target.value)}
                      className='px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sarafi-500 focus:border-sarafi-500'
                    >
                      <option value='all'>ټول رولونه</option>
                      {roles.map((role) => (
                        <option key={role.value} value={role.value}>
                          {role.label}
                        </option>
                      ))}
                    </select>
                    <select
                      value={filterStatus}
                      onChange={(e) => setFilterStatus(e.target.value)}
                      className='px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sarafi-500 focus:border-sarafi-500'
                    >
                      <option value='all'>ټول حالتونه</option>
                      <option value='active'>فعال</option>
                      <option value='inactive'>غیر فعال</option>
                      <option value='pending'>د تصدیق په انتظار</option>
                    </select>
                  </div>
                  <button
                    onClick={() => setShowAddModal(true)}
                    className='flex items-center px-4 py-2 bg-sarafi-600 text-white rounded-lg hover:bg-sarafi-700 transition-colors'
                  >
                    <Plus className='h-4 w-4 ml-2' />
                    <span className='pashto-text'>نوی کاروونکی</span>
                  </button>
                </div>

                {/* Users Table */}
                <div className='bg-white border border-gray-200 rounded-lg overflow-hidden'>
                  <div className='overflow-x-auto'>
                    <table className='min-w-full divide-y divide-gray-200'>
                      <thead className='bg-gray-50'>
                        <tr>
                          <th className='px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider pashto-text'>
                            کاروونکی
                          </th>
                          <th className='px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider pashto-text'>
                            رول
                          </th>
                          <th className='px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider pashto-text'>
                            حالت
                          </th>
                          <th className='px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider pashto-text'>
                            وروستی ننوتل
                          </th>
                          <th className='px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider pashto-text'>
                            عملیات
                          </th>
                        </tr>
                      </thead>
                      <tbody className='bg-white divide-y divide-gray-200'>
                        {filteredUsers.map((user) => (
                          <tr key={user.id} className='hover:bg-gray-50'>
                            <td className='px-6 py-4 whitespace-nowrap'>
                              <div className='flex items-center'>
                                <div className='h-10 w-10 bg-sarafi-100 rounded-full flex items-center justify-center ml-4'>
                                  <span className='text-sm font-medium text-sarafi-600'>
                                    {user.full_name.charAt(0)}
                                  </span>
                                </div>
                                <div>
                                  <div className='text-sm font-medium text-gray-900 pashto-text'>
                                    {user.full_name}
                                  </div>
                                  <div className='text-sm text-gray-500'>
                                    {user.username} • {user.email}
                                  </div>
                                  <div className='text-sm text-gray-500 pashto-text'>
                                    {user.phone}
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td className='px-6 py-4 whitespace-nowrap'>
                              <span
                                className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleColor(
                                  user.role
                                )}`}
                              >
                                {getRoleLabel(user.role)}
                              </span>
                            </td>
                            <td className='px-6 py-4 whitespace-nowrap'>
                              <div className='flex items-center'>
                                {getStatusIcon(user.status)}
                                <span className='mr-2 text-sm text-gray-900 pashto-text'>
                                  {getStatusLabel(user.status)}
                                </span>
                              </div>
                            </td>
                            <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500 pashto-text'>
                              {user.last_login}
                            </td>
                            <td className='px-6 py-4 whitespace-nowrap text-sm font-medium'>
                              <div className='flex items-center space-x-2'>
                                <button
                                  onClick={() => handleEditUser(user)}
                                  className='text-sarafi-600 hover:text-sarafi-900 p-1 rounded'
                                  title='ایډیټ'
                                >
                                  <Edit className='h-4 w-4' />
                                </button>
                                <button
                                  onClick={() => handleToggleStatus(user.id)}
                                  className={`p-1 rounded ${
                                    user.status === "active"
                                      ? "text-red-600 hover:text-red-900"
                                      : "text-green-600 hover:text-green-900"
                                  }`}
                                  title={
                                    user.status === "active"
                                      ? "غیر فعال کړئ"
                                      : "فعال کړئ"
                                  }
                                >
                                  {user.status === "active" ? (
                                    <EyeOff className='h-4 w-4' />
                                  ) : (
                                    <Eye className='h-4 w-4' />
                                  )}
                                </button>
                                {user.role !== "super_admin" && (
                                  <button
                                    onClick={() => handleDeleteUser(user.id)}
                                    className='text-red-600 hover:text-red-900 p-1 rounded'
                                    title='ړنګ کړئ'
                                  >
                                    <Trash2 className='h-4 w-4' />
                                  </button>
                                )}
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}

            {activeTab === "roles" && (
              <RoleManagement roles={roles} setRoles={setRoles} users={users} />
            )}

            {activeTab === "permissions" && (
              <PermissionMatrix users={users} setUsers={setUsers} />
            )}

            {activeTab === "settings" && (
              <div className='space-y-6'>
                <div className='bg-gray-50 rounded-lg p-6'>
                  <h3 className='text-lg font-medium text-gray-900 pashto-text mb-4'>
                    د سیسټم تنظیمات
                  </h3>
                  <p className='text-gray-600 pashto-text'>
                    دا برخه د راتلونکي پراختیا لپاره ساتل شوې ده.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Modals */}
      {showAddModal && (
        <AddUserModal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          onUserAdded={(newUser) => {
            setUsers([...users, { ...newUser, id: Date.now() }]);
            setShowAddModal(false);
            toast.show({ type: "success", message: "کاروونکی اضافه شو" });
          }}
          roles={roles}
        />
      )}

      {showEditModal && selectedUser && (
        <EditUserModal
          isOpen={showEditModal}
          user={selectedUser}
          onClose={() => {
            setShowEditModal(false);
            setSelectedUser(null);
          }}
          onUserUpdated={(updatedUser) => {
            setUsers(
              users.map((user) =>
                user.id === updatedUser.id ? updatedUser : user
              )
            );
            setShowEditModal(false);
            setSelectedUser(null);
            toast.show({ type: "success", message: "معلومات تصحیح شول" });
          }}
          roles={roles}
        />
      )}
    </Layout>
  );
};

export default AdminPanel;
