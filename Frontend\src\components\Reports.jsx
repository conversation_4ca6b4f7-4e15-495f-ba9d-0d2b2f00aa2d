import React, { useState, useEffect } from "react";
import {
  Bar<PERSON>hart3,
  Pie<PERSON>hart,
  TrendingUp,
  Download,
  Calendar,
  Filter,
  Users,
  DollarSign,
  BookOpen,
} from "lucide-react";
import Layout from "./shared/Layout";
import Button from "./shared/Button";
import FormInput from "./shared/FormInput";
import { useToast } from "./shared/Toast.jsx";
import {
  daybooksAPI,
  customersAPI,
  customerCurrencyBalancesAPI,
  customerCurrencyTransactionsAPI,
} from "../services/api.js";

const Reports = () => {
  const toast = useToast();
  const [dateRange, setDateRange] = useState({
    from: "",
    to: "",
  });
  const [reportType, setReportType] = useState("daily");
  const [loading, setLoading] = useState(false);
  const [reportData, setReportData] = useState({
    totalCustomers: 0,
    totalDaybooks: 0,
    totalNamBalance: 0,
    totalJamaBalance: 0,
    totalTransactions: 0,
    recentTransactions: [],
  });

  useEffect(() => {
    loadReportData();
  }, []);

  const loadReportData = async () => {
    setLoading(true);
    try {
      // Load all data for reports
      const [customers, daybooks, balances, transactions] = await Promise.all([
        customersAPI.getAll(),
        daybooksAPI.getAll(),
        customerCurrencyBalancesAPI.getAll(),
        customerCurrencyTransactionsAPI.getAll(),
      ]);

      const totalNamBalance = balances
        .filter((b) => b.currency_type === "NAM")
        .reduce((sum, b) => sum + parseFloat(b.balance), 0);

      const totalJamaBalance = balances
        .filter((b) => b.currency_type === "JAMA")
        .reduce((sum, b) => sum + parseFloat(b.balance), 0);

      setReportData({
        totalCustomers: customers.length || 0,
        totalDaybooks: daybooks.length || 0,
        totalNamBalance,
        totalJamaBalance,
        totalTransactions: transactions.length || 0,
        recentTransactions: transactions.slice(0, 10) || [],
      });
    } catch (error) {
      console.error("Error loading report data:", error);
      toast.show({
        type: "error",
        message: "د راپور د معلوماتو د لوډولو کې ستونزه",
      });
    } finally {
      setLoading(false);
    }
  };

  const reportTypes = [
    { value: "daily", label: "ورځني راپور" },
    { value: "weekly", label: "اونیز راپور" },
    { value: "monthly", label: "میاشتني راپور" },
    { value: "yearly", label: "کلني راپور" },
    { value: "custom", label: "ځانګړی راپور" },
  ];

  const handleDateRangeChange = (e) => {
    const { name, value } = e.target;
    setDateRange((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const generateReport = () => {
    console.log("Generating report...", { reportType, dateRange });
  };

  const exportReport = (format) => {
    console.log(`Exporting report as ${format}...`);
  };

  return (
    <Layout title='راپورونه او تحلیلونه'>
      <div className='space-y-6'>
        {/* د فلټر برخه */}
        <div className='bg-white rounded-lg shadow-md p-6'>
          <h2 className='text-lg font-semibold text-gray-900 mb-4 pashto-text'>
            د راپور تنظیمات
          </h2>

          <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
            <FormInput
              label='د راپور ډول'
              name='reportType'
              type='select'
              value={reportType}
              onChange={(e) => setReportType(e.target.value)}
              options={reportTypes}
              icon={BarChart3}
            />

            <FormInput
              label='د نیټې څخه'
              name='from'
              type='date'
              value={dateRange.from}
              onChange={handleDateRangeChange}
              icon={Calendar}
            />

            <FormInput
              label='د نیټې پورې'
              name='to'
              type='date'
              value={dateRange.to}
              onChange={handleDateRangeChange}
              icon={Calendar}
            />

            <div className='flex items-end'>
              <Button
                variant='primary'
                icon={Filter}
                onClick={generateReport}
                className='w-full'
              >
                راپور جوړول
              </Button>
            </div>
          </div>
        </div>

        {/* د راپورونو کارتونه */}
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
          {/* د عمومي راپور کارت */}
          <div className='bg-white rounded-lg shadow-md p-6'>
            <div className='flex items-center mb-4'>
              <div className='w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center ml-3'>
                <BarChart3 className='h-6 w-6 text-blue-600' />
              </div>
              <div>
                <h3 className='text-lg font-semibold text-gray-900 pashto-text'>
                  عمومي راپور
                </h3>
                <p className='text-sm text-gray-600 pashto-text'>
                  د ټولو راکړو ورکړو لنډیز
                </p>
              </div>
            </div>

            <div className='space-y-2 mb-4'>
              <div className='flex justify-between'>
                <span className='text-sm text-gray-600 pashto-text'>
                  ټولې راکړې ورکړې:
                </span>
                <span className='font-medium'>۱۲۳</span>
              </div>
              <div className='flex justify-between'>
                <span className='text-sm text-gray-600 pashto-text'>
                  ټوله جمعه:
                </span>
                <span className='font-medium text-green-600'>
                  ۲,۳۴۵,۶۷۸ افغانۍ
                </span>
              </div>
              <div className='flex justify-between'>
                <span className='text-sm text-gray-600 pashto-text'>
                  ټول نام:
                </span>
                <span className='font-medium text-red-600'>
                  ۱,۸۹۰,۱۲۳ افغانۍ
                </span>
              </div>
            </div>

            <div className='flex space-x-2'>
              <Button
                size='sm'
                variant='outline'
                onClick={() => exportReport("pdf")}
              >
                PDF
              </Button>
              <Button
                size='sm'
                variant='outline'
                onClick={() => exportReport("excel")}
              >
                Excel
              </Button>
            </div>
          </div>

          {/* د پیرودونکو راپور کارت */}
          <div className='bg-white rounded-lg shadow-md p-6'>
            <div className='flex items-center mb-4'>
              <div className='w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center ml-3'>
                <PieChart className='h-6 w-6 text-green-600' />
              </div>
              <div>
                <h3 className='text-lg font-semibold text-gray-900 pashto-text'>
                  د پیرودونکو راپور
                </h3>
                <p className='text-sm text-gray-600 pashto-text'>
                  د پیرودونکو د میزان تحلیل
                </p>
              </div>
            </div>

            <div className='space-y-2 mb-4'>
              <div className='flex justify-between'>
                <span className='text-sm text-gray-600 pashto-text'>
                  فعال پیرودونکي:
                </span>
                <span className='font-medium'>۹۸</span>
              </div>
              <div className='flex justify-between'>
                <span className='text-sm text-gray-600 pashto-text'>
                  مثبت میزان:
                </span>
                <span className='font-medium text-green-600'>۷۵</span>
              </div>
              <div className='flex justify-between'>
                <span className='text-sm text-gray-600 pashto-text'>
                  منفي میزان:
                </span>
                <span className='font-medium text-red-600'>۲۳</span>
              </div>
            </div>

            <div className='flex space-x-2'>
              <Button
                size='sm'
                variant='outline'
                onClick={() => exportReport("pdf")}
              >
                PDF
              </Button>
              <Button
                size='sm'
                variant='outline'
                onClick={() => exportReport("excel")}
              >
                Excel
              </Button>
            </div>
          </div>

          {/* د اسعارو راپور کارت */}
          <div className='bg-white rounded-lg shadow-md p-6'>
            <div className='flex items-center mb-4'>
              <div className='w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center ml-3'>
                <TrendingUp className='h-6 w-6 text-purple-600' />
              </div>
              <div>
                <h3 className='text-lg font-semibold text-gray-900 pashto-text'>
                  د اسعارو راپور
                </h3>
                <p className='text-sm text-gray-600 pashto-text'>
                  د اسعارو د بدلون تحلیل
                </p>
              </div>
            </div>

            <div className='space-y-2 mb-4'>
              <div className='flex justify-between'>
                <span className='text-sm text-gray-600 pashto-text'>
                  فعال اسعار:
                </span>
                <span className='font-medium'>۵</span>
              </div>
              <div className='flex justify-between'>
                <span className='text-sm text-gray-600 pashto-text'>
                  د ډالر نرخ:
                </span>
                <span className='font-medium'>۷۰.۵۰ افغانۍ</span>
              </div>
              <div className='flex justify-between'>
                <span className='text-sm text-gray-600 pashto-text'>
                  د یورو نرخ:
                </span>
                <span className='font-medium'>۷۶.۲۰ افغانۍ</span>
              </div>
            </div>

            <div className='flex space-x-2'>
              <Button
                size='sm'
                variant='outline'
                onClick={() => exportReport("pdf")}
              >
                PDF
              </Button>
              <Button
                size='sm'
                variant='outline'
                onClick={() => exportReport("excel")}
              >
                Excel
              </Button>
            </div>
          </div>
        </div>

        {/* د راپور پریکړه لیک */}
        <div className='bg-white rounded-lg shadow-md p-6'>
          <div className='flex items-center justify-between mb-4'>
            <h3 className='text-lg font-semibold text-gray-900 pashto-text'>
              د راپور پریکړه لیک
            </h3>
            <div className='flex space-x-2'>
              <Button variant='outline' icon={Download}>
                ټول راپورونه ایکسپورټ کړئ
              </Button>
            </div>
          </div>

          <div className='bg-gray-50 rounded-lg p-8 text-center'>
            <BarChart3 className='h-16 w-16 text-gray-400 mx-auto mb-4' />
            <h4 className='text-lg font-medium text-gray-900 mb-2 pashto-text'>
              د راپور ښودنه
            </h4>
            <p className='text-gray-600 pashto-text'>
              د راپور د ښودنې لپاره پورته د فلټر څخه کار واخلئ
            </p>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Reports;
