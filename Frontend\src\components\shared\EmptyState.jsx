import React from 'react';
import { FileX, Users, BookOpen, TrendingUp } from 'lucide-react';

const EmptyState = ({ 
  icon: Icon = FileX, 
  title = 'هیڅ معلومات نشته', 
  description = 'دلته هیڅ معلومات نه دي موندل شوي',
  action,
  className = ''
}) => {
  return (
    <div className={`text-center py-12 ${className}`}>
      <Icon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
      <h3 className="text-lg font-medium text-gray-900 pashto-text mb-2">{title}</h3>
      <p className="text-sm text-gray-500 pashto-text mb-6">{description}</p>
      {action && (
        <div>{action}</div>
      )}
    </div>
  );
};

export default EmptyState;
