from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from core.models import User

class Command(BaseCommand):
    help = 'Create admin user'

    def handle(self, *args, **options):
        User = get_user_model()
        
        # Delete existing admin user if exists
        try:
            admin_user = User.objects.get(username='admin')
            admin_user.delete()
            self.stdout.write('Deleted existing admin user')
        except User.DoesNotExist:
            pass
        
        # Create new admin user
        admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            full_name='System Administrator',
            role='super_admin',
            status='active',
            is_staff=True,
            is_superuser=True
        )
        
        self.stdout.write(
            self.style.SUCCESS(f'Successfully created admin user: {admin_user.username}')
        )
