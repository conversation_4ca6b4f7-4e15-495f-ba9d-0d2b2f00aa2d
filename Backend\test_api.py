#!/usr/bin/env python3
"""
Simple script to test API endpoints
"""
import requests
import json

BASE_URL = "http://127.0.0.1:8000/api"
TOKEN = "2a2ddf4149400e7220394b9a67144dea18ed6fee"

# Test without authentication first
def test_login_first():
    """Test login and get fresh token"""
    login_url = f"{BASE_URL}/auth/login/"
    login_data = {"username": "khan", "password": "khan123"}

    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            data = response.json()
            return data.get('token')
        else:
            print(f"❌ Login failed: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Login exception: {e}")
        return None

headers = {
    "Authorization": f"Token {TOKEN}",
    "Content-Type": "application/json"
}

def test_endpoint(endpoint, method="GET", data=None):
    url = f"{BASE_URL}{endpoint}"
    print(f"\n🔍 Testing {method} {url}")
    
    try:
        if method == "GET":
            response = requests.get(url, headers=headers)
        elif method == "POST":
            response = requests.post(url, headers=headers, json=data)
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Success!")
            if response.json():
                print(f"Data: {json.dumps(response.json(), indent=2)[:200]}...")
        else:
            print(f"❌ Error: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

# Test login endpoint
print("🔐 Testing login endpoint...")
login_data = {"username": "khan", "password": "khan123"}
test_endpoint("/auth/login/", "POST", login_data)

# Test login without authentication
print("\n🔐 Testing login endpoint without auth header...")
login_url = f"{BASE_URL}/auth/login/"
try:
    response = requests.post(login_url, json=login_data)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        print("✅ Login Success!")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
    else:
        print(f"❌ Login Error: {response.text}")
except Exception as e:
    print(f"❌ Exception: {e}")

# Get fresh token
print("🔑 Getting fresh authentication token...")
fresh_token = test_login_first()
if fresh_token:
    headers["Authorization"] = f"Token {fresh_token}"
    print(f"✅ Got fresh token: {fresh_token[:20]}...")
else:
    print("❌ Could not get fresh token, using old one")

# Test main endpoints
endpoints_to_test = [
    "/",
    "/daybooks/",
    "/customers/",
    "/companies/",
    "/currencies/",
    "/customer-balances/",
    "/jama-entries/",
    "/naam-entries/",
    "/transactions/",
    # Legacy endpoints
    "/daybook-transactions/",
    "/jama-transactions/",
    "/naam-transactions/",
    "/customer-currency-balances/",
]

for endpoint in endpoints_to_test:
    test_endpoint(endpoint)

print("\n🏁 API testing completed!")
