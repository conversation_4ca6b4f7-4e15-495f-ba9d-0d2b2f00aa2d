import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  BookOpen,
  Plus,
  Edit,
  Eye,
  Calendar,
  User,
  DollarSign,
  Lock,
  Unlock,
  Search,
  Filter,
} from "lucide-react";
import Layout from "./shared/Layout";
import Button from "./shared/Button";
import Badge from "./shared/Badge";
import LoadingSkeleton from "./shared/LoadingSkeleton";
import { useToast } from "./shared/Toast";
import { daybooksAPI } from "../services/api.js";

const DaybookList = () => {
  const navigate = useNavigate();
  const toast = useToast();
  const [daybooks, setDaybooks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [currentUser, setCurrentUser] = useState(null);

  // Sample data - replace with API call later
  const sampleDaybooks = [
    {
      id: 1,
      page_number: 15,
      date: "2024-01-15",
      created_by: {
        id: 1,
        name: "احمد علي خان",
        username: "ahmad",
      },
      status: "open",
      total_jamah: 125000,
      total_naam: 98000,
      balance: 27000,
      transaction_count: 8,
      created_at: "2024-01-15T10:30:00Z",
    },
    {
      id: 2,
      page_number: 16,
      date: "2024-01-16",
      created_by: {
        id: 2,
        name: "فاطمه احمد",
        username: "fatima",
      },
      status: "closed",
      total_jamah: 85000,
      total_naam: 75000,
      balance: 10000,
      transaction_count: 5,
      created_at: "2024-01-16T09:15:00Z",
    },
    {
      id: 3,
      page_number: 17,
      date: "2024-01-17",
      created_by: {
        id: 1,
        name: "احمد علي خان",
        username: "ahmad",
      },
      status: "open",
      total_jamah: 200000,
      total_naam: 180000,
      balance: 20000,
      transaction_count: 12,
      created_at: "2024-01-17T11:45:00Z",
    },
    {
      id: 4,
      page_number: 18,
      date: "2024-01-18",
      created_by: {
        id: 3,
        name: "محمد حسن",
        username: "hassan",
      },
      status: "open",
      total_jamah: 150000,
      total_naam: 140000,
      balance: 10000,
      transaction_count: 7,
      created_at: "2024-01-18T08:20:00Z",
    },
  ];

  useEffect(() => {
    loadDaybooks();
    loadCurrentUser();
  }, []);

  const loadCurrentUser = () => {
    // Get current user from localStorage
    const user = JSON.parse(localStorage.getItem("user") || "{}");
    setCurrentUser(user);
  };

  const loadDaybooks = async () => {
    setLoading(true);
    try {
      const response = await daybooksAPI.getAll();
      console.log("📋 DaybookList: Loaded daybooks:", response);

      // Handle different response structures
      let daybooksData = [];
      if (Array.isArray(response)) {
        daybooksData = response;
      } else if (response.results && Array.isArray(response.results)) {
        daybooksData = response.results;
      } else if (response.data && Array.isArray(response.data)) {
        daybooksData = response.data;
      } else {
        console.warn("Unexpected response structure:", response);
        daybooksData = [];
      }

      setDaybooks(daybooksData);
    } catch (error) {
      console.error("❌ DaybookList: Error loading daybooks:", error);

      let errorMessage = "د ورځني کتابونو د لوډولو کې ستونزه";
      if (error.response?.data?.detail) {
        errorMessage += `: ${error.response.data.detail}`;
      } else if (error.message) {
        errorMessage += `: ${error.message}`;
      }

      toast.show({
        type: "error",
        message: errorMessage,
      });

      // Fallback to sample data for development
      console.log("📋 DaybookList: Using sample data as fallback");
      setDaybooks(sampleDaybooks);
    } finally {
      setLoading(false);
    }
  };

  const canEditDaybook = (daybook) => {
    if (!currentUser) return false;

    // Super admin and admin can edit all daybooks
    if (currentUser.role === "super_admin" || currentUser.role === "admin") {
      return true;
    }

    // Creator can edit their own daybook
    return (
      daybook.created_by?.username === currentUser.username || daybook.can_edit
    );
  };

  const handleCreateNew = async () => {
    try {
      setLoading(true);
      console.log("🚀 Starting daybook creation...");

      // Check if user is authenticated
      const user = JSON.parse(localStorage.getItem("user") || "{}");
      console.log("👤 Current user:", user);

      if (!user.id) {
        throw new Error("User not authenticated");
      }

      const today = new Date().toISOString().split("T")[0];
      console.log("📅 Today's date:", today);

      // Get the next available page number for today
      let nextPageNumber = 1;
      try {
        console.log("📄 Fetching next page number...");
        nextPageNumber = await daybooksAPI.getNextPageNumber(today);
        console.log("📄 Next page number:", nextPageNumber);
      } catch (error) {
        console.log(
          "⚠️ Could not fetch next page number, using page number 1:",
          error
        );
      }

      const newDaybookData = {
        page_number: nextPageNumber,
        date: today,
        status: "open",
      };

      console.log("📝 Creating daybook with data:", newDaybookData);

      const response = await daybooksAPI.create(newDaybookData);
      console.log("✅ Daybook created successfully:", response);

      // Refresh the daybooks list
      await loadDaybooks();

      toast.show({
        type: "success",
        message: "نوی ورځني کتاب جوړ شو",
      });

      // Navigate to the new daybook for editing
      navigate(`/daybook/${response.id}/edit`);
    } catch (error) {
      console.error("❌ Error creating daybook:", error);
      console.error("❌ Error details:", {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
      });

      let errorMessage = "د نوي ورځني کتاب جوړولو کې ستونزه";

      if (error.response?.status === 401) {
        errorMessage = "تاسو د دې عمل لپاره اجازه نلرئ";
      } else if (error.response?.status === 403) {
        errorMessage = "تاسو د دې عمل لپاره اجازه نلرئ";
      } else if (error.response?.data?.non_field_errors) {
        errorMessage += `: ${error.response.data.non_field_errors.join(", ")}`;
      } else if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      } else if (error.message) {
        errorMessage += `: ${error.message}`;
      }

      toast.show({
        type: "error",
        message: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleView = (daybook) => {
    console.log("📖 DaybookList: Navigating to view daybook:", daybook.id);
    navigate(`/daybook/${daybook.id}/view`);
  };

  const handleEdit = (daybook) => {
    console.log("✏️ DaybookList: Attempting to edit daybook:", daybook.id);

    if (canEditDaybook(daybook)) {
      console.log(
        "✅ DaybookList: User has edit permission, navigating to edit mode"
      );
      navigate(`/daybook/${daybook.id}/edit`);
    } else {
      console.log("❌ DaybookList: User does not have edit permission");
      toast.show({
        type: "error",
        message: "تاسو د دې ورځني کتاب د تصحیح اجازه نلرئ",
      });
    }
  };

  const filteredDaybooks = (daybooks || []).filter((daybook) => {
    if (!daybook) return false;

    const matchesSearch =
      daybook.page_number?.toString().includes(searchTerm) ||
      (daybook.created_by?.name || daybook.created_by_name || "").includes(
        searchTerm
      ) ||
      (daybook.date || "").includes(searchTerm);

    const matchesStatus =
      statusFilter === "all" || daybook.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const formatDate = (dateString) => {
    if (!dateString) return "نامعلوم";
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("fa-AF");
    } catch (error) {
      return dateString; // Return original string if parsing fails
    }
  };

  const formatCurrency = (amount) => {
    if (amount === null || amount === undefined) return "0 افغانۍ";
    try {
      return amount.toLocaleString() + " افغانۍ";
    } catch (error) {
      return amount + " افغانۍ";
    }
  };

  if (loading) {
    return (
      <Layout title='د ورځني کتابونو لیست'>
        <LoadingSkeleton />
      </Layout>
    );
  }

  return (
    <Layout title='د ورځني کتابونو لیست'>
      <div className='space-y-8'>
        {/* Header */}
        <div className='bg-white rounded-lg shadow-md p-6 border border-gray-200'>
          <div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
            <div>
              <h1 className='text-2xl font-bold text-gray-900 pashto-text'>
                د ورځني کتابونو لیست
              </h1>
              <p className='text-gray-600 pashto-text mt-1'>
                ټول ورځني کتابونه دلته لیست شوي دي
              </p>
            </div>
            <Button
              variant='primary'
              icon={Plus}
              onClick={handleCreateNew}
              loading={loading}
              disabled={loading}
            >
              نوی ورځني کتاب
            </Button>
          </div>
        </div>

        {/* Filters */}
        <div className='bg-white rounded-lg shadow-md p-4 border border-gray-200'>
          <div className='flex flex-col sm:flex-row gap-4'>
            {/* Search */}
            <div className='flex-1'>
              <div className='relative'>
                <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4' />
                <input
                  type='text'
                  placeholder='د صفحې شمیره، جوړونکی یا نیټه پلټنه...'
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className='w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent pashto-text'
                />
              </div>
            </div>

            {/* Status Filter */}
            <div className='sm:w-48'>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent pashto-text'
              >
                <option value='all'>ټول حالتونه</option>
                <option value='open'>خلاص</option>
                <option value='closed'>تړل شوی</option>
              </select>
            </div>
          </div>
        </div>

        {/* Daybooks Grid */}
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
          {filteredDaybooks.map((daybook) => (
            <div
              key={daybook.id}
              className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 border border-gray-200'
            >
              <div className='p-6'>
                {/* Header */}
                <div className='flex items-center justify-between mb-4'>
                  <div className='flex items-center space-x-2'>
                    <BookOpen className='h-5 w-5 text-blue-600' />
                    <span className='font-bold text-lg text-gray-900 pashto-text'>
                      صفحه {daybook.page_number}
                    </span>
                  </div>
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      daybook.status === "open"
                        ? "bg-green-100 text-green-800"
                        : "bg-red-100 text-red-800"
                    }`}
                  >
                    {daybook.status === "open" ? "خلاص" : "تړل شوی"}
                  </span>
                </div>
                {/* Info */}
                <div className='space-y-3 mb-4'>
                  <div className='flex items-center text-sm text-gray-600'>
                    <Calendar className='h-4 w-4 ml-2' />
                    <span className='pashto-text'>
                      نیټه: {formatDate(daybook.date)}
                    </span>
                  </div>

                  <div className='flex items-center text-sm text-gray-600'>
                    <User className='h-4 w-4 ml-2' />
                    <span className='pashto-text'>
                      جوړونکی:{" "}
                      {daybook.created_by?.name ||
                        daybook.created_by_name ||
                        "نامعلوم"}
                    </span>
                  </div>

                  <div className='flex items-center text-sm text-gray-600'>
                    <DollarSign className='h-4 w-4 ml-2' />
                    <span className='pashto-text'>
                      میزان: {formatCurrency(daybook.balance)}
                    </span>
                  </div>
                </div>

                {/* Stats */}
                <div className='grid grid-cols-2 gap-4 mb-4'>
                  <div className='text-center p-2 bg-green-50 rounded'>
                    <div className='text-xs text-green-600 pashto-text'>
                      جمعه
                    </div>
                    <div className='font-bold text-green-700 text-sm'>
                      {formatCurrency(daybook.total_jamah)}
                    </div>
                  </div>
                  <div className='text-center p-2 bg-red-50 rounded'>
                    <div className='text-xs text-red-600 pashto-text'>نام</div>
                    <div className='font-bold text-red-700 text-sm'>
                      {formatCurrency(daybook.total_naam)}
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className='flex space-x-2'>
                  <Button
                    variant='outline'
                    size='sm'
                    icon={Eye}
                    onClick={() => handleView(daybook)}
                    className='flex-1'
                  >
                    کتل
                  </Button>

                  {canEditDaybook(daybook) ? (
                    <Button
                      variant='primary'
                      size='sm'
                      icon={Edit}
                      onClick={() => handleEdit(daybook)}
                      className='flex-1'
                    >
                      تصحیح
                    </Button>
                  ) : (
                    <Button
                      variant='outline'
                      size='sm'
                      icon={Lock}
                      disabled
                      className='flex-1'
                    >
                      بند
                    </Button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {filteredDaybooks.length === 0 && (
          <div className='text-center py-12'>
            <BookOpen className='h-12 w-12 text-gray-400 mx-auto mb-4' />
            <h3 className='text-lg font-medium text-gray-900 pashto-text mb-2'>
              هیڅ ورځني کتاب ونه موندل شو
            </h3>
            <p className='text-gray-600 pashto-text mb-4'>
              د نوي ورځني کتاب جوړولو لپاره لاندې تڼۍ کلیک کړئ
            </p>
            <Button
              variant='primary'
              icon={Plus}
              onClick={handleCreateNew}
              loading={loading}
              disabled={loading}
            >
              نوی ورځني کتاب
            </Button>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default DaybookList;
