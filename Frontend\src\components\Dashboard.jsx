import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Users,
  BookOpen,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Plus,
  Eye,
  ArrowUpRight,
  ArrowDownRight,
  Calendar,
  Clock,
  AlertCircle,
  CheckCircle,
  Wallet,
  BarChart3,
  FileText,
  Calculator,
  Settings,
} from "lucide-react";
import Layout from "./shared/Layout";
import SearchBar from "./shared/SearchBar.jsx";
import Badge from "./shared/Badge.jsx";
import LoadingSkeleton from "./shared/LoadingSkeleton.jsx";
import { useToast } from "./shared/Toast.jsx";
import Button from "./shared/Button.jsx";
import {
  daybooksAPI,
  customersAPI,
  customerCurrencyBalancesAPI,
  daybookTransactionsAPI,
} from "../services/api.js";

const Dashboard = () => {
  console.log("📊 Dashboard: Component initializing...");

  const toast = useToast();
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [dashboardData, setDashboardData] = useState({
    totalCustomers: 0,
    totalDaybooks: 0,
    totalNamBalance: 0,
    totalJamaBalance: 0,
    recentDaybooks: [],
    recentTransactions: [],
  });
  const navigate = useNavigate();

  useEffect(() => {
    console.log("📊 Dashboard: useEffect triggered, loading data...");

    // Check authentication before loading data
    const token = localStorage.getItem("token");
    const user = localStorage.getItem("user");

    console.log("📊 Dashboard: Auth check", {
      hasToken: !!token,
      hasUser: !!user,
      tokenLength: token?.length,
    });

    if (!token || !user) {
      console.error("❌ Dashboard: No auth data found, should redirect");
      return;
    }

    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    console.log("📊 Dashboard: Starting to load data...");
    setLoading(true);

    try {
      // Load customers
      console.log("👥 Dashboard: Loading customers...");
      const customersResponse = await customersAPI.getAll();
      console.log("👥 Dashboard: Raw customers response:", customersResponse);
      const customers = customersResponse.results || customersResponse;
      console.log("✅ Dashboard: Customers processed:", customers?.length || 0);

      // Load daybooks
      console.log("📚 Dashboard: Loading daybooks...");
      const daybooksResponse = await daybooksAPI.getAll();
      console.log("📚 Dashboard: Raw daybooks response:", daybooksResponse);
      const daybooks = daybooksResponse.results || daybooksResponse;
      console.log("✅ Dashboard: Daybooks processed:", daybooks?.length || 0);

      // Load currency balances
      console.log("💰 Dashboard: Loading currency balances...");
      const balancesResponse = await customerCurrencyBalancesAPI.getAll();
      console.log("💰 Dashboard: Raw balances response:", balancesResponse);
      const balances = balancesResponse.results || balancesResponse;
      console.log("✅ Dashboard: Balances processed:", balances?.length || 0);

      // Calculate totals
      const totalNamBalance = (balances || [])
        .filter((b) => b.currency_type === "NAM")
        .reduce((sum, b) => sum + parseFloat(b.balance || 0), 0);

      const totalJamaBalance = (balances || [])
        .filter((b) => b.currency_type === "JAMA")
        .reduce((sum, b) => sum + parseFloat(b.balance || 0), 0);

      // Get recent daybooks (last 5)
      const recentDaybooks = daybooks.slice(0, 5);

      // Load recent transactions
      console.log("📋 Dashboard: Loading transactions...");
      const transactionsResponse = await daybookTransactionsAPI.getAll();
      console.log(
        "📋 Dashboard: Raw transactions response:",
        transactionsResponse
      );
      const transactions = transactionsResponse.results || transactionsResponse;
      const recentTransactions = transactions.slice(0, 5);
      console.log(
        "✅ Dashboard: Transactions processed:",
        transactions?.length || 0
      );

      console.log("📊 Dashboard: Setting final data...");
      setDashboardData({
        totalCustomers: (customers || []).length,
        totalDaybooks: (daybooks || []).length,
        totalNamBalance,
        totalJamaBalance,
        recentDaybooks: recentDaybooks || [],
        recentTransactions: recentTransactions || [],
      });
      console.log("✅ Dashboard: Data loading completed successfully");
    } catch (error) {
      console.error("❌ Dashboard: Error loading dashboard data:", error);
      console.error("❌ Dashboard: Error details:", {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        stack: error.stack,
      });

      toast.show({
        type: "error",
        message: "د کورپاڼې د معلوماتو د لوډولو کې ستونزه",
      });
    } finally {
      console.log("📊 Dashboard: Setting loading to false");
      setLoading(false);
    }
  };

  const handleAddTransaction = () => {
    toast.show({ type: "info", message: "د نوې معاملې فورم خلاصیږي..." });
    navigate("/daybook-list");
  };

  const handleAddCustomer = () => {
    toast.show({ type: "info", message: "د نوي پیرودونکي فورم خلاصیږي..." });
    navigate("/customers");
  };

  const handleCurrencyManagement = () => {
    toast.show({ type: "info", message: "د اسعارو مدیریت پاڼې ته ځي..." });
    navigate("/customer-currency");
  };

  const handleViewAll = (section) => {
    toast.show({
      type: "info",
      message: `د ${section} بشپړ لیست ښودل کیږي...`,
    });
  };

  // د آمار کارتونه
  const statsCards = [
    {
      title: "ټول پیرودونکي",
      value: dashboardData.totalCustomers.toString(),
      icon: Users,
      color: "bg-blue-500",
      change: "+۵%",
      changeType: "positive",
      subtitle: "د تیرې میاشتې څخه",
    },
    {
      title: "ټول نام میزان",
      value: dashboardData.totalNamBalance.toLocaleString(),
      currency: "نام",
      icon: TrendingUp,
      color: "bg-green-500",
      change: "+۱۲%",
      changeType: "positive",
      subtitle: "د ټولو پیرودونکو",
    },
    {
      title: "ټول جمعه میزان",
      value: dashboardData.totalJamaBalance.toLocaleString(),
      currency: "جمعه",
      icon: TrendingDown,
      color: "bg-purple-500",
      change: "+۸%",
      changeType: "positive",
      subtitle: "د ټولو پیرودونکو",
    },
    {
      title: "ټول ورځني کتابونه",
      value: dashboardData.totalDaybooks.toString(),
      icon: BookOpen,
      color: "bg-orange-500",
      change: "+۳",
      changeType: "positive",
      subtitle: "ټول کتابونه",
    },
    {
      title: "ټول میزان",
      value: (
        dashboardData.totalNamBalance + dashboardData.totalJamaBalance
      ).toLocaleString(),
      currency: "ټوله",
      icon: Wallet,
      color: "bg-indigo-500",
      change: "+۲.۵%",
      changeType: "positive",
      subtitle: "نام + جمعه",
    },
  ];

  // د منیو آیټمونه
  const menuItems = [
    {
      name: "کورپاڼه",
      icon: TrendingUp,
      path: "/dashboard",
      active: true,
      badge: null,
    },
    { name: "پیرودونکي", icon: Users, path: "/customers", badge: "۱۲۳" },
    { name: "ورځني کتاب", icon: BookOpen, path: "/daybook-list", badge: "۷" },
    { name: "د پیرودونکي کاتا", icon: FileText, path: "/ledger", badge: null },
    { name: "راپورونه", icon: BarChart3, path: "/reports", badge: null },
    { name: "حسابات", icon: Calculator, path: "/accounts", badge: null },
    ...(Users?.role === "super_admin"
      ? [{ name: "د اډمین پینل", icon: Settings, path: "/admin", badge: null }]
      : []),
    { name: "تنظیمات", icon: Settings, path: "/settings", badge: null },
  ];
  console.log(Users);
  // د اخیرو فعالیتونو ډیټا
  const recentTransactions = [
    {
      id: 1,
      customerName: "احمد علي",
      type: "جمعه",
      amount: "۵,۰۰۰",
      currency: "افغانۍ",
      time: "۱۰:۳۰ صبح",
      status: "بشپړ",
    },
    {
      id: 2,
      customerName: "فاطمه خان",
      type: "نام",
      amount: "۲,۵۰۰",
      currency: "افغانۍ",
      time: "۱۰:۱۵ صبح",
      status: "بشپړ",
    },
    {
      id: 3,
      customerName: "محمد حسن",
      type: "جمعه",
      amount: "۱۰,۰۰۰",
      currency: "افغانۍ",
      time: "۹:۴۵ صبح",
      status: "بشپړ",
    },
    {
      id: 4,
      customerName: "عایشه احمد",
      type: "نام",
      amount: "۷,۵۰۰",
      currency: "افغانۍ",
      time: "۹:۳۰ صبح",
      status: "بشپړ",
    },
    {
      id: 5,
      customerName: "علي رضا",
      type: "جمعه",
      amount: "۳,۰۰۰",
      currency: "افغانۍ",
      time: "۹:۱۵ صبح",
      status: "بشپړ",
    },
  ];

  // د نویو پیرودونکو ډیټا
  const newCustomers = [
    {
      id: 1,
      name: "محمد حسن",
      phone: "۰۷۹۹۱۲۳۴۵۶",
      registrationTime: "د نن ورځې",
      status: "فعال",
    },
    {
      id: 2,
      name: "فریده احمد",
      phone: "۰۷۰۰۹۸۷۶۵۴",
      registrationTime: "د نن ورځې",
      status: "فعال",
    },
    {
      id: 3,
      name: "احمد شاه",
      phone: "۰۷۸۸۵۵۵۱۲۳",
      registrationTime: "پرون",
      status: "فعال",
    },
    {
      id: 4,
      name: "مریم خان",
      phone: "۰۷۷۷۴۴۴۳۲۱",
      registrationTime: "پرون",
      status: "فعال",
    },
    {
      id: 5,
      name: "عبدالله رحیم",
      phone: "۰۷۶۶۳۳۳۲۱۰",
      registrationTime: "۲ ورځې دمخه",
      status: "فعال",
    },
  ];

  return (
    <Layout title='کورپاڼه'>
      <div className='space-y-6'>
        {/* Header with search and actions */}
        <div className='flex items-center justify-between'>
          <div>
            <h1 className='text-2xl font-bold text-gray-900 pashto-text'>
              کورپاڼه
            </h1>
            <p className='text-gray-600 pashto-text'>د نن ورځې لنډیز</p>
          </div>
          <div className='flex space-x-3'>
            <Button onClick={handleCurrencyManagement} variant='outline'>
              <Wallet className='h-4 w-4 ml-2' />د اسعارو مدیریت
            </Button>
            <Button onClick={handleAddCustomer} variant='secondary'>
              <Plus className='h-4 w-4 ml-2' />
              نوی پیرودونکی
            </Button>
            <Button onClick={handleAddTransaction} variant='primary'>
              <Plus className='h-4 w-4 ml-2' />
              نوې معامله
            </Button>
          </div>
        </div>

        {/* Search Bar */}
        <div className='bg-white p-4 rounded-lg shadow-md'>
          <SearchBar
            value={searchTerm}
            onChange={setSearchTerm}
            placeholder='د پیرودونکي، معاملې یا حساب لټون...'
            onClear={() => setSearchTerm("")}
          />
        </div>

        {/* Stats Cards */}
        {loading ? (
          <LoadingSkeleton type='stats' />
        ) : (
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
            {statsCards.map((card, index) => (
              <div key={index} className='bg-white rounded-lg shadow-md p-6'>
                <div className='flex items-center'>
                  <div
                    className={`w-12 h-12 ${card.color} rounded-lg flex items-center justify-center ml-4`}
                  >
                    <card.icon className='h-6 w-6 text-white' />
                  </div>
                  <div className='flex-1'>
                    <p className='text-sm font-medium text-gray-600 pashto-text'>
                      {card.title}
                    </p>
                    <div className='flex items-center'>
                      <p className='text-2xl font-bold text-gray-900'>
                        {card.value}{" "}
                        {card.currency && (
                          <span className='text-sm text-gray-500'>
                            {card.currency}
                          </span>
                        )}
                      </p>
                    </div>
                    <div className='flex items-center mt-1'>
                      {card.changeType === "positive" ? (
                        <ArrowUpRight className='h-4 w-4 text-green-500 ml-1' />
                      ) : (
                        <ArrowDownRight className='h-4 w-4 text-red-500 ml-1' />
                      )}
                      <span
                        className={`text-sm ${
                          card.changeType === "positive"
                            ? "text-green-600"
                            : "text-red-600"
                        }`}
                      >
                        {card.change}
                      </span>
                      <span className='text-sm text-gray-500 mr-2 pashto-text'>
                        {card.subtitle}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Recent Transactions and New Customers */}
        <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
          {/* Recent Transactions */}
          <div className='bg-white rounded-lg shadow-md'>
            <div className='p-6 border-b border-gray-200'>
              <div className='flex items-center justify-between'>
                <h3 className='text-lg font-medium text-gray-900 pashto-text'>
                  وروستي معاملات
                </h3>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={() => handleViewAll("معاملات")}
                >
                  <Eye className='h-4 w-4 ml-1' />
                  ټول وګورئ
                </Button>
              </div>
            </div>
            <div className='p-6'>
              {loading ? (
                <LoadingSkeleton type='table' rows={3} />
              ) : (
                <div className='space-y-4'>
                  {recentTransactions.map((transaction) => (
                    <div
                      key={transaction.id}
                      className='flex items-center justify-between p-3 bg-gray-50 rounded-lg'
                    >
                      <div className='flex items-center'>
                        <div
                          className={`w-10 h-10 rounded-lg flex items-center justify-center ml-3 ${
                            transaction.type === "جمعه"
                              ? "bg-green-100"
                              : "bg-blue-100"
                          }`}
                        >
                          {transaction.type === "جمعه" ? (
                            <ArrowUpRight
                              className={`h-5 w-5 ${
                                transaction.type === "جمعه"
                                  ? "text-green-600"
                                  : "text-blue-600"
                              }`}
                            />
                          ) : (
                            <ArrowDownRight className='h-5 w-5 text-blue-600' />
                          )}
                        </div>
                        <div>
                          <p className='text-sm font-medium text-gray-900 pashto-text'>
                            {transaction.customer}
                          </p>
                          <p className='text-xs text-gray-500 pashto-text'>
                            {transaction.time}
                          </p>
                        </div>
                      </div>
                      <div className='text-left'>
                        <Badge
                          variant={
                            transaction.type === "جمعه" ? "success" : "info"
                          }
                        >
                          {transaction.amount} افغانۍ
                        </Badge>
                        <p className='text-xs text-gray-500 mt-1 pashto-text'>
                          {transaction.status}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* New Customers */}
          <div className='bg-white rounded-lg shadow-md'>
            <div className='p-6 border-b border-gray-200'>
              <div className='flex items-center justify-between'>
                <h3 className='text-lg font-medium text-gray-900 pashto-text'>
                  نوي پیرودونکي
                </h3>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={() => handleViewAll("پیرودونکي")}
                >
                  <Eye className='h-4 w-4 ml-1' />
                  ټول وګورئ
                </Button>
              </div>
            </div>
            <div className='p-6'>
              {loading ? (
                <LoadingSkeleton type='table' rows={3} />
              ) : (
                <div className='space-y-4'>
                  {newCustomers.map((customer) => (
                    <div
                      key={customer.id}
                      className='flex items-center justify-between p-3 bg-gray-50 rounded-lg'
                    >
                      <div className='flex items-center'>
                        <div className='w-10 h-10 bg-sarafi-100 rounded-lg flex items-center justify-center ml-3'>
                          <Users className='h-5 w-5 text-sarafi-600' />
                        </div>
                        <div>
                          <p className='text-sm font-medium text-gray-900 pashto-text'>
                            {customer.name}
                          </p>
                          <p className='text-xs text-gray-500 font-mono'>
                            {customer.phone}
                          </p>
                        </div>
                      </div>
                      <div className='text-left'>
                        <Badge variant='success'>{customer.status}</Badge>
                        <p className='text-xs text-gray-500 mt-1 pashto-text'>
                          {customer.registrationTime}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Dashboard;
