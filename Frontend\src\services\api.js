import axios from "axios";

// Base API configuration
const API_BASE_URL = "http://localhost:8000/api";

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
  timeout: 10000, // 10 second timeout
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("token");
    console.log("🔐 API Request:", {
      url: config.url,
      method: config.method,
      hasToken: !!token,
      tokenLength: token?.length,
    });

    if (token) {
      config.headers.Authorization = `Token ${token}`;
      console.log("✅ API: Token added to request");
    } else {
      console.log("⚠️ API: No token found for request");
    }
    return config;
  },
  (error) => {
    console.error("❌ API Request Error:", error);
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    console.log("✅ API Response:", {
      url: response.config.url,
      status: response.status,
      dataType: typeof response.data,
      hasData: !!response.data,
    });
    return response;
  },
  (error) => {
    console.error("❌ API Error:", error);
    console.error("❌ API Error Details:", {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message,
    });

    if (error.response?.status === 401) {
      console.error("🚨 401 UNAUTHORIZED - Clearing auth and redirecting");
      // Unauthorized - clear token and redirect to login
      localStorage.removeItem("token");
      localStorage.removeItem("user");
      // Only redirect if not already on login page
      if (window.location.pathname !== "/login") {
        console.log("🔄 Redirecting to login page");
        window.location.href = "/login";
      } else {
        console.log("⚠️ Already on login page, not redirecting");
      }
    }

    // Log detailed error information
    if (error.response) {
      console.error("📤 Response error:", error.response.data);
      console.error("📊 Status:", error.response.status);
    } else if (error.request) {
      console.error("📡 Request error:", error.request);
    } else {
      console.error("💥 Error message:", error.message);
    }

    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: async (credentials) => {
    console.log("🔐 AuthAPI: Attempting login with:", {
      username: credentials.username,
    });
    const response = await api.post("/auth/login/", credentials);
    console.log("✅ AuthAPI: Login successful, response:", response.data);
    return response.data;
  },

  logout: async () => {
    console.log("🚪 AuthAPI: Attempting logout");
    const response = await api.post("/auth/logout/");
    console.log("✅ AuthAPI: Logout successful");
    return response.data;
  },

  getCurrentUser: async () => {
    console.log("👤 AuthAPI: Getting current user");
    const response = await api.get("/auth/user/");
    console.log("✅ AuthAPI: Current user retrieved:", response.data);
    return response.data;
  },
};

// Users API
export const usersAPI = {
  getAll: async (params = {}) => {
    const response = await api.get("/users/", { params });
    return response.data;
  },

  getById: async (id) => {
    const response = await api.get(`/users/${id}/`);
    return response.data;
  },

  create: async (userData) => {
    const response = await api.post("/users/", userData);
    return response.data;
  },

  update: async (id, userData) => {
    const response = await api.put(`/users/${id}/`, userData);
    return response.data;
  },

  delete: async (id) => {
    const response = await api.delete(`/users/${id}/`);
    return response.data;
  },
};

// Companies API
export const companiesAPI = {
  getAll: async (params = {}) => {
    const response = await api.get("/companies/", { params });
    return response.data;
  },

  getById: async (id) => {
    const response = await api.get(`/companies/${id}/`);
    return response.data;
  },

  create: async (companyData) => {
    const response = await api.post("/companies/", companyData);
    return response.data;
  },

  update: async (id, companyData) => {
    const response = await api.put(`/companies/${id}/`, companyData);
    return response.data;
  },

  delete: async (id) => {
    const response = await api.delete(`/companies/${id}/`);
    return response.data;
  },
};

// Customers API
export const customersAPI = {
  getAll: async (params = {}) => {
    console.log("👥 CustomersAPI: Getting all customers with params:", params);
    const response = await api.get("/customers/", { params });
    console.log(
      "✅ CustomersAPI: Customers retrieved:",
      response.data?.length || response.data?.results?.length || 0
    );
    return response.data;
  },

  getById: async (id) => {
    const response = await api.get(`/customers/${id}/`);
    return response.data;
  },

  create: async (customerData) => {
    console.log("👤 CustomersAPI: Creating customer with data:", customerData);
    const response = await api.post("/customers/", customerData);
    console.log(
      "✅ CustomersAPI: Customer created successfully:",
      response.data
    );
    return response.data;
  },

  update: async (id, customerData) => {
    console.log("✏️ CustomersAPI: Updating customer:", id, customerData);
    const response = await api.put(`/customers/${id}/`, customerData);
    console.log(
      "✅ CustomersAPI: Customer updated successfully:",
      response.data
    );
    return response.data;
  },

  delete: async (id) => {
    console.log("🗑️ CustomersAPI: Deleting customer with ID:", id);
    const response = await api.delete(`/customers/${id}/`);
    console.log("✅ CustomersAPI: Customer deleted successfully");
    return response.data;
  },

  getCurrencyBalances: async (customerId) => {
    const response = await api.get(
      `/customers/${customerId}/currency_balances/`
    );
    return response.data;
  },

  getCurrencyTransactions: async (customerId) => {
    const response = await api.get(
      `/customers/${customerId}/currency_transactions/`
    );
    return response.data;
  },
};

// Daybooks API
export const daybooksAPI = {
  getAll: async (params = {}) => {
    const response = await api.get("/daybooks/", { params });
    return response.data;
  },

  getById: async (id) => {
    const response = await api.get(`/daybooks/${id}/`);
    return response.data;
  },

  create: async (daybookData) => {
    const response = await api.post("/daybooks/", daybookData);
    return response.data;
  },

  update: async (id, daybookData) => {
    const response = await api.put(`/daybooks/${id}/`, daybookData);
    return response.data;
  },

  delete: async (id) => {
    const response = await api.delete(`/daybooks/${id}/`);
    return response.data;
  },

  getMyDaybooks: async () => {
    const response = await api.get("/daybooks/my_daybooks/");
    return response.data;
  },

  getNextPageNumber: async (date) => {
    const response = await api.get("/daybooks/next_page_number/", {
      params: { date },
    });
    return response.data.next_page_number;
  },

  saveWithTransactions: async (
    id,
    daybookData,
    jamaTransactions,
    naamTransactions
  ) => {
    console.log("💾 DaybooksAPI: Saving daybook with separate transactions:", {
      daybookId: id,
      jamaCount: jamaTransactions.length,
      naamCount: naamTransactions.length,
    });
    const response = await api.post(`/daybooks/${id}/save_with_transactions/`, {
      daybook: daybookData,
      jama_transactions: jamaTransactions,
      naam_transactions: naamTransactions,
    });
    console.log("✅ DaybooksAPI: Daybook saved successfully");
    return response.data;
  },
};

// Daybook Transactions API
export const daybookTransactionsAPI = {
  getAll: async (params = {}) => {
    const response = await api.get("/daybook-transactions/", { params });
    return response.data;
  },

  getById: async (id) => {
    const response = await api.get(`/daybook-transactions/${id}/`);
    return response.data;
  },

  create: async (transactionData) => {
    const response = await api.post("/daybook-transactions/", transactionData);
    return response.data;
  },

  update: async (id, transactionData) => {
    const response = await api.put(
      `/daybook-transactions/${id}/`,
      transactionData
    );
    return response.data;
  },

  delete: async (id) => {
    const response = await api.delete(`/daybook-transactions/${id}/`);
    return response.data;
  },

  getByDaybook: async (daybookId) => {
    const response = await api.get("/daybook-transactions/", {
      params: { daybook: daybookId },
    });
    return response.data;
  },
};

// Jama Transactions API
export const jamaTransactionsAPI = {
  getAll: async (params = {}) => {
    const response = await api.get("/jama-transactions/", { params });
    return response.data;
  },

  getById: async (id) => {
    const response = await api.get(`/jama-transactions/${id}/`);
    return response.data;
  },

  create: async (transactionData) => {
    console.log(
      "💰 JamaTransactionsAPI: Creating jama transaction:",
      transactionData
    );
    const response = await api.post("/jama-transactions/", transactionData);
    console.log(
      "✅ JamaTransactionsAPI: Jama transaction created successfully"
    );
    return response.data;
  },

  update: async (id, transactionData) => {
    const response = await api.put(
      `/jama-transactions/${id}/`,
      transactionData
    );
    return response.data;
  },

  delete: async (id) => {
    const response = await api.delete(`/jama-transactions/${id}/`);
    return response.data;
  },

  getByDaybook: async (daybookId) => {
    const response = await api.get("/jama-transactions/", {
      params: { daybook: daybookId },
    });
    return response.data;
  },
};

// Naam Transactions API
export const naamTransactionsAPI = {
  getAll: async (params = {}) => {
    const response = await api.get("/naam-transactions/", { params });
    return response.data;
  },

  getById: async (id) => {
    const response = await api.get(`/naam-transactions/${id}/`);
    return response.data;
  },

  create: async (transactionData) => {
    console.log(
      "💸 NaamTransactionsAPI: Creating naam transaction:",
      transactionData
    );
    const response = await api.post("/naam-transactions/", transactionData);
    console.log(
      "✅ NaamTransactionsAPI: Naam transaction created successfully"
    );
    return response.data;
  },

  update: async (id, transactionData) => {
    const response = await api.put(
      `/naam-transactions/${id}/`,
      transactionData
    );
    return response.data;
  },

  delete: async (id) => {
    const response = await api.delete(`/naam-transactions/${id}/`);
    return response.data;
  },

  getByDaybook: async (daybookId) => {
    const response = await api.get("/naam-transactions/", {
      params: { daybook: daybookId },
    });
    return response.data;
  },
};

// Customer Currency Balances API
export const customerCurrencyBalancesAPI = {
  getAll: async (params = {}) => {
    const response = await api.get("/customer-currency-balances/", { params });
    return response.data;
  },

  getById: async (id) => {
    const response = await api.get(`/customer-currency-balances/${id}/`);
    return response.data;
  },

  create: async (balanceData) => {
    const response = await api.post(
      "/customer-currency-balances/",
      balanceData
    );
    return response.data;
  },

  update: async (id, balanceData) => {
    const response = await api.put(
      `/customer-currency-balances/${id}/`,
      balanceData
    );
    return response.data;
  },

  delete: async (id) => {
    const response = await api.delete(`/customer-currency-balances/${id}/`);
    return response.data;
  },
};

// Customer Currency Transactions API
export const customerCurrencyTransactionsAPI = {
  getAll: async (params = {}) => {
    const response = await api.get("/customer-currency-transactions/", {
      params,
    });
    return response.data;
  },

  getById: async (id) => {
    const response = await api.get(`/customer-currency-transactions/${id}/`);
    return response.data;
  },

  create: async (transactionData) => {
    const response = await api.post(
      "/customer-currency-transactions/",
      transactionData
    );
    return response.data;
  },

  update: async (id, transactionData) => {
    const response = await api.put(
      `/customer-currency-transactions/${id}/`,
      transactionData
    );
    return response.data;
  },

  delete: async (id) => {
    const response = await api.delete(`/customer-currency-transactions/${id}/`);
    return response.data;
  },

  transfer: async (transferData) => {
    const response = await api.post(
      "/customer-currency-transactions/transfer/",
      transferData
    );
    return response.data;
  },
};

export default api;
