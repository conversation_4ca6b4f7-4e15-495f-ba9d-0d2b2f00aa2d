import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

const SimpleTest = () => {
  const [authData, setAuthData] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    console.log("🧪 SimpleTest: Component mounted");
    
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');
    
    console.log("🔍 SimpleTest: Checking auth data", {
      hasToken: !!token,
      hasUser: !!user,
      token: token,
      user: user
    });
    
    if (token && user) {
      try {
        const parsedUser = JSON.parse(user);
        setAuthData({
          token,
          user: parsedUser
        });
        console.log("✅ SimpleTest: Auth data is valid");
      } catch (error) {
        console.error("❌ SimpleTest: Failed to parse user data", error);
        setAuthData({ error: 'Invalid user data' });
      }
    } else {
      console.log("❌ SimpleTest: Missing auth data");
      setAuthData({ error: 'Missing token or user' });
    }
  }, []);

  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    navigate('/login');
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>🧪 Simple Authentication Test</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <button onClick={() => navigate('/dashboard')} style={{ margin: '5px' }}>
          Go to Dashboard
        </button>
        <button onClick={() => navigate('/auth-test')} style={{ margin: '5px' }}>
          Go to Auth Test
        </button>
        <button onClick={logout} style={{ margin: '5px' }}>
          Logout
        </button>
      </div>
      
      <div style={{ 
        background: '#f5f5f5', 
        padding: '15px', 
        borderRadius: '5px',
        whiteSpace: 'pre-wrap'
      }}>
        <h3>Authentication Status:</h3>
        {authData ? (
          authData.error ? (
            <div style={{ color: 'red' }}>
              ❌ Error: {authData.error}
            </div>
          ) : (
            <div style={{ color: 'green' }}>
              ✅ Authenticated as: {authData.user.full_name} ({authData.user.username})
              <br />
              Token: {authData.token.substring(0, 20)}...
              <br />
              Role: {authData.user.role}
              <br />
              Status: {authData.user.status}
            </div>
          )
        ) : (
          <div>⏳ Loading...</div>
        )}
      </div>
      
      <div style={{ marginTop: '20px' }}>
        <h3>Browser Storage:</h3>
        <div style={{ 
          background: '#e8e8e8', 
          padding: '10px', 
          borderRadius: '5px',
          fontSize: '12px',
          fontFamily: 'monospace'
        }}>
          Token: {localStorage.getItem('token') || 'Not found'}
          <br />
          User: {localStorage.getItem('user') || 'Not found'}
        </div>
      </div>
    </div>
  );
};

export default SimpleTest;
