import React from 'react';
import { X } from 'lucide-react';

const FilterPill = ({ 
  label, 
  value, 
  onRemove, 
  className = '' 
}) => {
  return (
    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm bg-sarafi-100 text-sarafi-800 ${className}`}>
      <span className="pashto-text">{label}: {value}</span>
      {onRemove && (
        <button
          onClick={onRemove}
          className="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full text-sarafi-600 hover:bg-sarafi-200 hover:text-sarafi-800 focus:outline-none"
        >
          <X className="w-3 h-3" />
        </button>
      )}
    </span>
  );
};

export default FilterPill;
