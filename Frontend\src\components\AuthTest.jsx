import React, { useState } from "react";
import { authAPI } from "../services/api.js";

const AuthTest = () => {
  const [result, setResult] = useState("");
  const [loading, setLoading] = useState(false);

  const testLogin = async () => {
    setLoading(true);
    setResult("Testing login...\n");

    try {
      // Test if backend is reachable
      setResult((prev) => prev + "🔍 Testing backend connection...\n");

      const response = await authAPI.login({
        username: "admin",
        password: "admin123",
      });

      setResult((prev) => prev + `✅ Login successful!\n`);
      setResult((prev) => prev + `Token: ${response.token}\n`);
      setResult(
        (prev) => prev + `User: ${JSON.stringify(response.user, null, 2)}\n`
      );

      // Store in localStorage
      localStorage.setItem("token", response.token);
      localStorage.setItem("user", JSON.stringify(response.user));

      setResult((prev) => prev + `💾 Stored in localStorage successfully\n`);

      // Test if we can access a protected endpoint
      setResult((prev) => prev + `🔐 Testing protected endpoint...\n`);
      try {
        const userResponse = await authAPI.getCurrentUser();
        setResult(
          (prev) =>
            prev + `✅ Protected endpoint works: ${userResponse.full_name}\n`
        );
      } catch (protectedError) {
        setResult(
          (prev) =>
            prev + `❌ Protected endpoint failed: ${protectedError.message}\n`
        );
      }
    } catch (error) {
      setResult((prev) => prev + `❌ Login failed: ${error.message}\n`);
      if (error.response) {
        setResult((prev) => prev + `Status: ${error.response.status}\n`);
        setResult(
          (prev) =>
            prev + `Response: ${JSON.stringify(error.response.data, null, 2)}\n`
        );
      } else if (error.request) {
        setResult(
          (prev) => prev + `❌ Network error - Backend might be down\n`
        );
        setResult(
          (prev) =>
            prev +
            `Check if Django server is running on http://localhost:8000\n`
        );
      }
    } finally {
      setLoading(false);
    }
  };

  const testCurrentUser = async () => {
    setLoading(true);
    setResult("Testing current user...");

    try {
      const response = await authAPI.getCurrentUser();
      setResult(`✅ Current user: ${JSON.stringify(response, null, 2)}`);
    } catch (error) {
      setResult(`❌ Current user failed: ${error.message}
Response: ${JSON.stringify(error.response?.data, null, 2)}`);
    } finally {
      setLoading(false);
    }
  };

  const checkStorage = () => {
    const token = localStorage.getItem("token");
    const user = localStorage.getItem("user");

    setResult(`Storage check:
Token: ${token ? "EXISTS" : "MISSING"}
User: ${user ? "EXISTS" : "MISSING"}
Token value: ${token}
User value: ${user}`);
  };

  const clearStorage = () => {
    localStorage.removeItem("token");
    localStorage.removeItem("user");
    setResult("✅ Storage cleared");
  };

  const testBackendStatus = async () => {
    setLoading(true);
    setResult("Testing backend status...\n");

    try {
      // Test basic connectivity
      const response = await fetch("http://localhost:8000/api/");
      if (response.ok) {
        setResult((prev) => prev + "✅ Backend is reachable\n");
        setResult((prev) => prev + `Status: ${response.status}\n`);
      } else {
        setResult(
          (prev) => prev + `❌ Backend returned error: ${response.status}\n`
        );
      }
    } catch (error) {
      setResult((prev) => prev + `❌ Cannot reach backend: ${error.message}\n`);
      setResult(
        (prev) =>
          prev + "Make sure Django server is running on http://localhost:8000\n"
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: "20px", fontFamily: "monospace" }}>
      <h2>🔧 Authentication Test</h2>

      <div style={{ marginBottom: "20px" }}>
        <button
          onClick={testBackendStatus}
          disabled={loading}
          style={{ margin: "5px" }}
        >
          Test Backend
        </button>
        <button
          onClick={testLogin}
          disabled={loading}
          style={{ margin: "5px" }}
        >
          Test Login
        </button>
        <button
          onClick={testCurrentUser}
          disabled={loading}
          style={{ margin: "5px" }}
        >
          Test Current User
        </button>
        <button onClick={checkStorage} style={{ margin: "5px" }}>
          Check Storage
        </button>
        <button onClick={clearStorage} style={{ margin: "5px" }}>
          Clear Storage
        </button>
      </div>

      <div
        style={{
          background: "#f5f5f5",
          padding: "15px",
          borderRadius: "5px",
          whiteSpace: "pre-wrap",
          minHeight: "200px",
        }}
      >
        {loading ? "⏳ Loading..." : result || "Click a button to test"}
      </div>
    </div>
  );
};

export default AuthTest;
