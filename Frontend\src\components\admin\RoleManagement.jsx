import React, { useState } from "react";
import {
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  <PERSON>,
  <PERSON><PERSON><PERSON>riangle,
  CheckCircle,
} from "lucide-react";

const RoleManagement = ({ roles, setRoles, users }) => {
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingRole, setEditingRole] = useState(null);
  const [formData, setFormData] = useState({
    value: "",
    label: "",
    color: "bg-blue-100 text-blue-800",
    description: "",
    permissions: {
      dashboard: { read: true, write: false, delete: false },
      customers: { read: true, write: false, delete: false },
      transactions: { read: true, write: false, delete: false },
      reports: { read: true, write: false, delete: false },
      settings: { read: false, write: false, delete: false },
      admin: { read: false, write: false, delete: false },
    },
  });
  const [errors, setErrors] = useState({});

  const colorOptions = [
    { value: "bg-red-100 text-red-800", label: "سور", preview: "bg-red-100" },
    {
      value: "bg-blue-100 text-blue-800",
      label: "آسماني",
      preview: "bg-blue-100",
    },
    {
      value: "bg-green-100 text-green-800",
      label: "شین",
      preview: "bg-green-100",
    },
    {
      value: "bg-yellow-100 text-yellow-800",
      label: "ژیړ",
      preview: "bg-yellow-100",
    },
    {
      value: "bg-purple-100 text-purple-800",
      label: "بنفشي",
      preview: "bg-purple-100",
    },
    {
      value: "bg-pink-100 text-pink-800",
      label: "ګلابي",
      preview: "bg-pink-100",
    },
    {
      value: "bg-indigo-100 text-indigo-800",
      label: "نیلي",
      preview: "bg-indigo-100",
    },
    {
      value: "bg-gray-100 text-gray-800",
      label: "خاکستري",
      preview: "bg-gray-100",
    },
  ];

  const permissions = [
    { key: "dashboard", label: "کورپاڼه" },
    { key: "customers", label: "پیرودونکي" },
    { key: "transactions", label: "راکړې ورکړې" },
    { key: "reports", label: "راپورونه" },
    { key: "settings", label: "تنظیمات" },
    { key: "admin", label: "اډمین پینل" },
  ];

  const resetForm = () => {
    setFormData({
      value: "",
      label: "",
      color: "bg-blue-100 text-blue-800",
      description: "",
      permissions: {
        dashboard: { read: true, write: false, delete: false },
        customers: { read: true, write: false, delete: false },
        transactions: { read: true, write: false, delete: false },
        reports: { read: true, write: false, delete: false },
        settings: { read: false, write: false, delete: false },
        admin: { read: false, write: false, delete: false },
      },
    });
    setErrors({});
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const handlePermissionChange = (permissionKey, type, value) => {
    setFormData((prev) => ({
      ...prev,
      permissions: {
        ...prev.permissions,
        [permissionKey]: {
          ...prev.permissions[permissionKey],
          [type]: value,
        },
      },
    }));
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.value.trim()) {
      newErrors.value = "د رول کوډ اړین دی";
    } else if (!/^[a-z_]+$/.test(formData.value)) {
      newErrors.value = "د رول کوډ باید یوازې کشر انګلیسي توري او _ ولري";
    } else if (
      roles.some(
        (role) =>
          role.value === formData.value && role.value !== editingRole?.value
      )
    ) {
      newErrors.value = "دا رول کوډ دمخه شته";
    }

    if (!formData.label.trim()) {
      newErrors.label = "د رول نوم اړین دی";
    }

    if (!formData.description.trim()) {
      newErrors.description = "د رول تشریح اړین دی";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (validateForm()) {
      if (editingRole) {
        // Update existing role
        setRoles((prevRoles) =>
          prevRoles.map((role) =>
            role.value === editingRole.value ? { ...formData } : role
          )
        );
      } else {
        // Add new role
        setRoles((prevRoles) => [...prevRoles, { ...formData }]);
      }
      handleCloseModal();
    }
  };

  const handleEdit = (role) => {
    setEditingRole(role);
    setFormData({ ...role });
    setShowAddModal(true);
  };

  const handleDelete = (roleValue) => {
    // Check if role is in use
    const usersWithRole = users.filter((user) => user.role === roleValue);
    if (usersWithRole.length > 0) {
      alert(
        `دا رول د ${usersWithRole.length} کاروونکو لخوا کارول کیږي. لومړی د دغو کاروونکو رولونه بدل کړئ.`
      );
      return;
    }

    // Prevent deletion of super_admin
    if (roleValue === "super_admin") {
      alert("د لوی مدیر رول د ړنګولو وړ نه دی");
      return;
    }

    if (window.confirm("ایا تاسو ډاډه یاست چې دا رول ړنګ کړئ؟")) {
      setRoles((prevRoles) =>
        prevRoles.filter((role) => role.value !== roleValue)
      );
    }
  };

  const handleCloseModal = () => {
    setShowAddModal(false);
    setEditingRole(null);
    resetForm();
  };

  const getUserCountForRole = (roleValue) => {
    return users.filter((user) => user.role === roleValue).length;
  };

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h3 className='text-lg font-medium text-gray-900 pashto-text'>
            د کاروونکو ډولونه
          </h3>
          <p className='text-sm text-gray-600 pashto-text'>
            دلته تاسو کولی شئ نوي کاروونکو ډولونه اضافه کړئ او د هغوی اجازې
            وټاکئ
          </p>
        </div>
        <button
          onClick={() => setShowAddModal(true)}
          className='flex items-center px-4 py-2 bg-sarafi-600 text-white rounded-lg hover:bg-sarafi-700 transition-colors'
        >
          <Plus className='h-4 w-4 ml-2' />
          <span className='pashto-text'>نوی رول</span>
        </button>
      </div>

      {/* Roles Grid */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
        {roles.map((role) => (
          <div
            key={role.value}
            className='bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow'
          >
            <div className='flex items-center justify-between mb-4'>
              <div className='flex items-center'>
                <Shield className='h-5 w-5 text-gray-400 ml-2' />
                <span
                  className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${role.color}`}
                >
                  {role.label}
                </span>
              </div>
              <div className='flex items-center space-x-2'>
                {role.value !== "super_admin" && (
                  <>
                    <button
                      onClick={() => handleEdit(role)}
                      className='text-sarafi-600 hover:text-sarafi-900 p-1 rounded'
                      title='ایډیټ'
                    >
                      <Edit className='h-4 w-4' />
                    </button>
                    <button
                      onClick={() => handleDelete(role.value)}
                      className='text-red-600 hover:text-red-900 p-1 rounded'
                      title='ړنګ کړئ'
                    >
                      <Trash2 className='h-4 w-4' />
                    </button>
                  </>
                )}
              </div>
            </div>

            <div className='space-y-3'>
              <div>
                <p className='text-sm text-gray-600 pashto-text'>
                  {role.description || "د دغه رول لپاره تشریح نشته"}
                </p>
              </div>

              <div className='flex items-center justify-between text-sm'>
                <span className='text-gray-500 pashto-text'>کاروونکي:</span>
                <span className='font-medium text-gray-900'>
                  {getUserCountForRole(role.value)}
                </span>
              </div>

              <div className='flex items-center justify-between text-sm'>
                <span className='text-gray-500 pashto-text'>کوډ:</span>
                <span className='font-mono text-gray-900'>{role.value}</span>
              </div>

              {/* Permission Summary */}
              <div className='pt-3 border-t border-gray-100'>
                <p className='text-xs text-gray-500 pashto-text mb-2'>اجازې:</p>
                <div className='flex flex-wrap gap-1'>
                  {permissions.map((permission) => {
                    const hasAnyPermission =
                      role.permissions?.[permission.key]?.read ||
                      role.permissions?.[permission.key]?.write ||
                      role.permissions?.[permission.key]?.delete;
                    return (
                      <span
                        key={permission.key}
                        className={`inline-flex items-center px-2 py-0.5 rounded text-xs ${
                          hasAnyPermission
                            ? "bg-green-100 text-green-800"
                            : "bg-gray-100 text-gray-500"
                        }`}
                      >
                        {hasAnyPermission ? (
                          <CheckCircle className='h-3 w-3 ml-1' />
                        ) : (
                          <X className='h-3 w-3 ml-1' />
                        )}
                        {permission.label}
                      </span>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Add/Edit Role Modal */}
      {showAddModal && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4'>
          <div className='bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto'>
            <div className='flex items-center justify-between p-6 border-b border-gray-200'>
              <h2 className='text-xl font-semibold text-gray-900 pashto-text'>
                {editingRole ? "رول ایډیټ کړئ" : "نوی رول اضافه کړئ"}
              </h2>
              <button
                onClick={handleCloseModal}
                className='text-gray-400 hover:text-gray-600 transition-colors'
              >
                <X className='h-6 w-6' />
              </button>
            </div>

            <form onSubmit={handleSubmit} className='p-6 space-y-6'>
              {/* Basic Information */}
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div>
                  <label className='block text-sm font-medium text-gray-700 pashto-text mb-2'>
                    د رول کوډ *
                  </label>
                  <input
                    type='text'
                    name='value'
                    value={formData.value}
                    onChange={handleInputChange}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-sarafi-500 focus:border-sarafi-500 ${
                      errors.value ? "border-red-500" : "border-gray-300"
                    }`}
                    placeholder='manager, cashier, etc.'
                    disabled={editingRole?.value === "super_admin"}
                  />
                  {errors.value && (
                    <p className='mt-1 text-sm text-red-600 pashto-text'>
                      {errors.value}
                    </p>
                  )}
                  <p className='mt-1 text-xs text-gray-500'>
                    یوازې کشر انګلیسي توري او _ کارول کیږي
                  </p>
                </div>

                <div>
                  <label className='block text-sm font-medium text-gray-700 pashto-text mb-2'>
                    د رول نوم *
                  </label>
                  <input
                    type='text'
                    name='label'
                    value={formData.label}
                    onChange={handleInputChange}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-sarafi-500 focus:border-sarafi-500 ${
                      errors.label ? "border-red-500" : "border-gray-300"
                    }`}
                    placeholder='د رول نوم ولیکئ'
                    dir='rtl'
                  />
                  {errors.label && (
                    <p className='mt-1 text-sm text-red-600 pashto-text'>
                      {errors.label}
                    </p>
                  )}
                </div>

                <div>
                  <label className='block text-sm font-medium text-gray-700 pashto-text mb-2'>
                    رنګ
                  </label>
                  <div className='grid grid-cols-4 gap-2'>
                    {colorOptions.map((color) => (
                      <label
                        key={color.value}
                        className='flex items-center cursor-pointer'
                      >
                        <input
                          type='radio'
                          name='color'
                          value={color.value}
                          checked={formData.color === color.value}
                          onChange={handleInputChange}
                          className='sr-only'
                        />
                        <div
                          className={`w-8 h-8 rounded-full border-2 ${
                            color.preview
                          } ${
                            formData.color === color.value
                              ? "border-sarafi-500 ring-2 ring-sarafi-200"
                              : "border-gray-300"
                          }`}
                          title={color.label}
                        ></div>
                      </label>
                    ))}
                  </div>
                </div>

                <div>
                  <label className='block text-sm font-medium text-gray-700 pashto-text mb-2'>
                    مثال
                  </label>
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${formData.color}`}
                  >
                    {formData.label || "د رول نوم"}
                  </span>
                </div>
              </div>

              <div>
                <label className='block text-sm font-medium text-gray-700 pashto-text mb-2'>
                  تشریح *
                </label>
                <textarea
                  name='description'
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={3}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-sarafi-500 focus:border-sarafi-500 ${
                    errors.description ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder='د دغه رول د دندو تشریح ولیکئ'
                  dir='rtl'
                />
                {errors.description && (
                  <p className='mt-1 text-sm text-red-600 pashto-text'>
                    {errors.description}
                  </p>
                )}
              </div>

              {/* Permissions */}
              <div>
                <h3 className='text-lg font-medium text-gray-900 pashto-text mb-4'>
                  اجازې
                </h3>
                <div className='bg-gray-50 rounded-lg p-4'>
                  <div className='grid grid-cols-1 gap-4'>
                    {permissions.map((permission) => (
                      <div
                        key={permission.key}
                        className='flex items-center justify-between py-2'
                      >
                        <span className='text-sm font-medium text-gray-700 pashto-text'>
                          {permission.label}
                        </span>
                        <div className='flex items-center space-x-4'>
                          <label className='flex items-center'>
                            <input
                              type='checkbox'
                              checked={
                                formData.permissions[permission.key]?.read ||
                                false
                              }
                              onChange={(e) =>
                                handlePermissionChange(
                                  permission.key,
                                  "read",
                                  e.target.checked
                                )
                              }
                              className='h-4 w-4 text-sarafi-600 focus:ring-sarafi-500 border-gray-300 rounded'
                              disabled={
                                editingRole?.value === "super_admin" &&
                                permission.key === "admin"
                              }
                            />
                            <span className='mr-2 text-sm text-gray-600 pashto-text'>
                              لوستل
                            </span>
                          </label>
                          <label className='flex items-center'>
                            <input
                              type='checkbox'
                              checked={
                                formData.permissions[permission.key]?.write ||
                                false
                              }
                              onChange={(e) =>
                                handlePermissionChange(
                                  permission.key,
                                  "write",
                                  e.target.checked
                                )
                              }
                              className='h-4 w-4 text-sarafi-600 focus:ring-sarafi-500 border-gray-300 rounded'
                              disabled={
                                editingRole?.value === "super_admin" &&
                                permission.key === "admin"
                              }
                            />
                            <span className='mr-2 text-sm text-gray-600 pashto-text'>
                              لیکل
                            </span>
                          </label>
                          <label className='flex items-center'>
                            <input
                              type='checkbox'
                              checked={
                                formData.permissions[permission.key]?.delete ||
                                false
                              }
                              onChange={(e) =>
                                handlePermissionChange(
                                  permission.key,
                                  "delete",
                                  e.target.checked
                                )
                              }
                              className='h-4 w-4 text-sarafi-600 focus:ring-sarafi-500 border-gray-300 rounded'
                              disabled={
                                editingRole?.value === "super_admin" &&
                                permission.key === "admin"
                              }
                            />
                            <span className='mr-2 text-sm text-gray-600 pashto-text'>
                              ړنګول
                            </span>
                          </label>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className='flex items-center justify-end space-x-4 pt-6 border-t border-gray-200'>
                <button
                  type='button'
                  onClick={handleCloseModal}
                  className='px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sarafi-500'
                >
                  <span className='pashto-text'>لغوه کول</span>
                </button>
                <button
                  type='submit'
                  className='px-4 py-2 text-sm font-medium text-white bg-sarafi-600 border border-transparent rounded-lg hover:bg-sarafi-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sarafi-500'
                >
                  <span className='pashto-text'>
                    {editingRole ? "بدلونونه ساتل" : "رول اضافه کول"}
                  </span>
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default RoleManagement;
