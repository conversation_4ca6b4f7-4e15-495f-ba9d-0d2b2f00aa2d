import React, { useState } from "react";
import {
  Shield,
  Check,
  X,
  Edit,
  Save,
  RotateCcw,
  AlertTriangle,
  Info,
} from "lucide-react";

const PermissionMatrix = ({ users, setUsers }) => {
  const [editingUser, setEditingUser] = useState(null);
  const [tempPermissions, setTempPermissions] = useState({});

  const permissions = [
    { key: "dashboard", label: "کورپاڼه", description: "د اصلي کورپاڼې ته لاسرسی" },
    { key: "customers", label: "پیرودونکي", description: "د پیرودونکو مدیریت" },
    { key: "transactions", label: "راکړې ورکړې", description: "د مالي راکړو ورکړو مدیریت" },
    { key: "reports", label: "راپورونه", description: "د راپورونو لیدل او جوړول" },
    { key: "settings", label: "تنظیمات", description: "د سیسټم تنظیمات" },
    { key: "admin", label: "اډمین پینل", description: "د کاروونکو او اجازو مدیریت" },
  ];

  const permissionTypes = [
    { key: "read", label: "لوستل", icon: "👁️", color: "text-blue-600" },
    { key: "write", label: "لیکل", icon: "✏️", color: "text-green-600" },
    { key: "delete", label: "ړنګول", icon: "🗑️", color: "text-red-600" },
  ];

  const roles = [
    { value: "super_admin", label: "لوی مدیر", color: "bg-red-100 text-red-800" },
    { value: "manager", label: "مدیر", color: "bg-blue-100 text-blue-800" },
    { value: "cashier", label: "د پیسو ساتونکی", color: "bg-green-100 text-green-800" },
    { value: "viewer", label: "لیدونکی", color: "bg-gray-100 text-gray-800" },
  ];

  const getRoleLabel = (role) => {
    const roleObj = roles.find((r) => r.value === role);
    return roleObj ? roleObj.label : role;
  };

  const getRoleColor = (role) => {
    const roleObj = roles.find((r) => r.value === role);
    return roleObj ? roleObj.color : "bg-gray-100 text-gray-800";
  };

  const handleEditPermissions = (user) => {
    setEditingUser(user.id);
    setTempPermissions(JSON.parse(JSON.stringify(user.permissions)));
  };

  const handlePermissionChange = (permissionKey, type, value) => {
    setTempPermissions((prev) => ({
      ...prev,
      [permissionKey]: {
        ...prev[permissionKey],
        [type]: value,
      },
    }));
  };

  const handleSavePermissions = () => {
    setUsers((prevUsers) =>
      prevUsers.map((user) =>
        user.id === editingUser
          ? { ...user, permissions: tempPermissions }
          : user
      )
    );
    setEditingUser(null);
    setTempPermissions({});
  };

  const handleCancelEdit = () => {
    setEditingUser(null);
    setTempPermissions({});
  };

  const getPermissionIcon = (hasPermission) => {
    return hasPermission ? (
      <Check className="h-4 w-4 text-green-600" />
    ) : (
      <X className="h-4 w-4 text-red-400" />
    );
  };

  const resetToRoleDefaults = (userId, role) => {
    let defaultPermissions = {};

    switch (role) {
      case "super_admin":
        defaultPermissions = {
          dashboard: { read: true, write: true, delete: true },
          customers: { read: true, write: true, delete: true },
          transactions: { read: true, write: true, delete: true },
          reports: { read: true, write: true, delete: true },
          settings: { read: true, write: true, delete: true },
          admin: { read: true, write: true, delete: true },
        };
        break;
      case "manager":
        defaultPermissions = {
          dashboard: { read: true, write: true, delete: false },
          customers: { read: true, write: true, delete: false },
          transactions: { read: true, write: true, delete: false },
          reports: { read: true, write: false, delete: false },
          settings: { read: true, write: false, delete: false },
          admin: { read: false, write: false, delete: false },
        };
        break;
      case "cashier":
        defaultPermissions = {
          dashboard: { read: true, write: false, delete: false },
          customers: { read: true, write: true, delete: false },
          transactions: { read: true, write: true, delete: false },
          reports: { read: true, write: false, delete: false },
          settings: { read: false, write: false, delete: false },
          admin: { read: false, write: false, delete: false },
        };
        break;
      case "viewer":
        defaultPermissions = {
          dashboard: { read: true, write: false, delete: false },
          customers: { read: true, write: false, delete: false },
          transactions: { read: true, write: false, delete: false },
          reports: { read: true, write: false, delete: false },
          settings: { read: false, write: false, delete: false },
          admin: { read: false, write: false, delete: false },
        };
        break;
      default:
        return;
    }

    if (editingUser === userId) {
      setTempPermissions(defaultPermissions);
    } else {
      setUsers((prevUsers) =>
        prevUsers.map((user) =>
          user.id === userId
            ? { ...user, permissions: defaultPermissions }
            : user
        )
      );
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 ml-3" />
          <div>
            <h3 className="text-sm font-medium text-blue-800 pashto-text">
              د اجازو میټرکس
            </h3>
            <p className="mt-1 text-sm text-blue-700 pashto-text">
              دلته تاسو کولی شئ د هر کاروونکي لپاره جلا جلا اجازې وټاکئ. د هر برخې لپاره درې ډوله اجازې شته: لوستل، لیکل، او ړنګول.
            </p>
          </div>
        </div>
      </div>

      {/* Permission Matrix Table */}
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider pashto-text">
                  کاروونکی
                </th>
                {permissions.map((permission) => (
                  <th
                    key={permission.key}
                    className="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    <div className="flex flex-col items-center">
                      <span className="pashto-text mb-1">{permission.label}</span>
                      <div className="flex space-x-1">
                        {permissionTypes.map((type) => (
                          <span
                            key={type.key}
                            className={`text-xs ${type.color} pashto-text`}
                            title={type.label}
                          >
                            {type.icon}
                          </span>
                        ))}
                      </div>
                    </div>
                  </th>
                ))}
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider pashto-text">
                  عملیات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {users.map((user) => (
                <tr key={user.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="h-8 w-8 bg-sarafi-100 rounded-full flex items-center justify-center ml-3">
                        <span className="text-xs font-medium text-sarafi-600">
                          {user.full_name.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900 pashto-text">
                          {user.full_name}
                        </div>
                        <div className="text-xs text-gray-500">
                          {user.username}
                        </div>
                        <span
                          className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getRoleColor(
                            user.role
                          )}`}
                        >
                          {getRoleLabel(user.role)}
                        </span>
                      </div>
                    </div>
                  </td>
                  {permissions.map((permission) => (
                    <td key={permission.key} className="px-3 py-4 text-center">
                      <div className="flex justify-center space-x-2">
                        {permissionTypes.map((type) => {
                          const currentPermissions =
                            editingUser === user.id
                              ? tempPermissions
                              : user.permissions;
                          const hasPermission =
                            currentPermissions[permission.key]?.[type.key] ||
                            false;

                          if (editingUser === user.id) {
                            return (
                              <label
                                key={type.key}
                                className="flex items-center cursor-pointer"
                                title={`${permission.label} - ${type.label}`}
                              >
                                <input
                                  type="checkbox"
                                  checked={hasPermission}
                                  onChange={(e) =>
                                    handlePermissionChange(
                                      permission.key,
                                      type.key,
                                      e.target.checked
                                    )
                                  }
                                  className="h-3 w-3 text-sarafi-600 focus:ring-sarafi-500 border-gray-300 rounded"
                                  disabled={
                                    user.role === "super_admin" &&
                                    permission.key === "admin"
                                  }
                                />
                              </label>
                            );
                          }

                          return (
                            <div
                              key={type.key}
                              className="flex items-center justify-center"
                              title={`${permission.label} - ${type.label}`}
                            >
                              {getPermissionIcon(hasPermission)}
                            </div>
                          );
                        })}
                      </div>
                    </td>
                  ))}
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    {editingUser === user.id ? (
                      <div className="flex items-center justify-center space-x-2">
                        <button
                          onClick={handleSavePermissions}
                          className="text-green-600 hover:text-green-900 p-1 rounded"
                          title="ساتل"
                        >
                          <Save className="h-4 w-4" />
                        </button>
                        <button
                          onClick={handleCancelEdit}
                          className="text-gray-600 hover:text-gray-900 p-1 rounded"
                          title="لغوه کول"
                        >
                          <X className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => resetToRoleDefaults(user.id, user.role)}
                          className="text-blue-600 hover:text-blue-900 p-1 rounded"
                          title="د رول ډیفالټ اجازې"
                        >
                          <RotateCcw className="h-4 w-4" />
                        </button>
                      </div>
                    ) : (
                      <div className="flex items-center justify-center space-x-2">
                        <button
                          onClick={() => handleEditPermissions(user)}
                          className="text-sarafi-600 hover:text-sarafi-900 p-1 rounded"
                          title="ایډیټ"
                          disabled={user.role === "super_admin"}
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => resetToRoleDefaults(user.id, user.role)}
                          className="text-blue-600 hover:text-blue-900 p-1 rounded"
                          title="د رول ډیفالټ اجازې"
                          disabled={user.role === "super_admin"}
                        >
                          <RotateCcw className="h-4 w-4" />
                        </button>
                      </div>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Legend */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="text-sm font-medium text-gray-900 pashto-text mb-3">
          د اجازو تشریح
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {permissionTypes.map((type) => (
            <div key={type.key} className="flex items-center">
              <span className="text-lg ml-2">{type.icon}</span>
              <div>
                <span className={`text-sm font-medium ${type.color} pashto-text`}>
                  {type.label}
                </span>
                <p className="text-xs text-gray-500 pashto-text">
                  {type.key === "read" && "معلومات لیدل"}
                  {type.key === "write" && "معلومات اضافه کول او بدلول"}
                  {type.key === "delete" && "معلومات ړنګول"}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PermissionMatrix;
